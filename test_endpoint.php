<?php

// <PERSON>ript pour tester l'endpoint user-assigned
echo "=== TEST DE L'ENDPOINT USER-ASSIGNED ===\n";

// D'abord, récupérer un token d'employé
$loginUrl = 'http://************:8000/api/auth/login';
$loginData = [
    'email' => '<EMAIL>',  // Email de l'utilisateur ID 5
    'password' => 'password'      // Mot de passe par défaut
];

echo "1. CONNEXION DE L'UTILISATEUR:\n";
echo "Email: {$loginData['email']}\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $loginUrl);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($loginData));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);

$loginResponse = curl_exec($ch);
$loginHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($loginHttpCode === 200) {
    $loginData = json_decode($loginResponse, true);
    
    if (isset($loginData['data']['token'])) {
        $token = $loginData['data']['token'];
        echo "✅ Connexion réussie!\n";
        echo "Token: " . substr($token, 0, 20) . "...\n";
        echo "Utilisateur: {$loginData['data']['user']['name']}\n";
        echo "ID: {$loginData['data']['user']['id']}\n";
        
        // Maintenant tester l'endpoint user-assigned
        echo "\n2. TEST DE L'ENDPOINT USER-ASSIGNED:\n";
        
        $assignedUrl = 'http://************:8000/api/sites/user-assigned';
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $assignedUrl);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $token,
            'Accept: application/json'
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        $assignedResponse = curl_exec($ch);
        $assignedHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        echo "Code de réponse: $assignedHttpCode\n";
        echo "Réponse:\n";
        
        $assignedData = json_decode($assignedResponse, true);
        if ($assignedData) {
            echo json_encode($assignedData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
            
            if ($assignedHttpCode === 200 && isset($assignedData['success']) && $assignedData['success']) {
                echo "\n✅ SUCCÈS! L'endpoint fonctionne correctement!\n";
                echo "Site assigné: {$assignedData['data']['name']}\n";
                echo "ID du site: {$assignedData['data']['id']}\n";
                echo "Coordonnées: {$assignedData['data']['latitude']}, {$assignedData['data']['longitude']}\n";
                
                echo "\n🎯 SOLUTION CONFIRMÉE:\n";
                echo "- L'utilisateur ID 5 a bien un site assigné\n";
                echo "- L'endpoint /api/sites/user-assigned fonctionne\n";
                echo "- Le frontend peut maintenant récupérer le site\n";
                
            } else {
                echo "\n❌ PROBLÈME DÉTECTÉ:\n";
                if (isset($assignedData['message_ar'])) {
                    echo "Message: {$assignedData['message_ar']}\n";
                }
            }
        } else {
            echo "Réponse brute: $assignedResponse\n";
        }
        
    } else {
        echo "❌ Token non trouvé dans la réponse de connexion\n";
        echo "Réponse: $loginResponse\n";
    }
} else {
    echo "❌ Échec de la connexion (Code: $loginHttpCode)\n";
    echo "Réponse: $loginResponse\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "TEST TERMINÉ\n";
echo str_repeat("=", 60) . "\n";
