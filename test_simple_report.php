<?php

// Simple test to isolate the report generation issue
echo "🔧 Simple Report Generation Test\n";
echo "===============================\n\n";

try {
    // Change to the Laravel directory
    chdir('C:\wamp64\www\clockin');
    
    // Include Laravel bootstrap
    require_once 'vendor/autoload.php';
    
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "✅ Laravel bootstrapped successfully\n";
    
    // Test database connection
    try {
        $users = \App\Models\User::count();
        echo "✅ Database connected - Found $users users\n";
    } catch (Exception $e) {
        echo "❌ Database connection failed: " . $e->getMessage() . "\n";
        exit(1);
    }
    
    // Test if user 5 exists
    $user = \App\Models\User::find(5);
    if (!$user) {
        echo "❌ User with ID 5 not found\n";
        exit(1);
    }
    echo "✅ User found: {$user->name}\n";
    
    // Test date parsing
    $startDate = \Carbon\Carbon::parse('2025-07-28')->startOfDay();
    $endDate = \Carbon\Carbon::parse('2025-08-04')->endOfDay();
    echo "✅ Dates parsed: {$startDate} to {$endDate}\n";
    
    // Test if we can query pointages for this user
    $pointagesCount = \App\Models\Pointage::where('user_id', 5)
        ->whereBetween('debut_pointage', [$startDate, $endDate])
        ->count();
    echo "✅ Found $pointagesCount pointages for user in date range\n";
    
    // Test ExportService instantiation
    $exportService = new \App\Services\ExportService();
    echo "✅ ExportService instantiated\n";
    
    // Test the prepareIndividualEmployeeData method (reflection)
    $reflection = new ReflectionClass($exportService);
    $method = $reflection->getMethod('prepareIndividualEmployeeData');
    $method->setAccessible(true);
    
    echo "🔄 Preparing individual employee data...\n";
    $data = $method->invoke($exportService, $user, $startDate, $endDate);
    echo "✅ Data prepared successfully\n";
    echo "📊 Data structure:\n";
    echo "   - User: {$data['user']->name}\n";
    echo "   - Pointages count: " . count($data['pointages']) . "\n";
    echo "   - Statistics: " . json_encode($data['statistics'], JSON_PRETTY_PRINT) . "\n";
    
    // Test Excel export creation
    echo "🔄 Testing Excel export...\n";
    
    // Check if storage directory is writable
    $storageDir = storage_path('app');
    if (!is_writable($storageDir)) {
        echo "❌ Storage directory not writable: $storageDir\n";
        exit(1);
    }
    echo "✅ Storage directory writable: $storageDir\n";
    
    // Try to create the export
    $export = new \App\Exports\DetailedPointagesExport($data);
    echo "✅ DetailedPointagesExport created\n";
    
    // Test Excel facade
    $filename = 'test_report_' . time() . '.xlsx';
    echo "🔄 Generating Excel file: $filename\n";
    
    \Maatwebsite\Excel\Facades\Excel::store($export, $filename, 'local');
    
    $filePath = storage_path('app/' . $filename);
    if (file_exists($filePath)) {
        $fileSize = filesize($filePath);
        echo "✅ Excel file created successfully: $filePath ($fileSize bytes)\n";
        
        // Clean up test file
        unlink($filePath);
        echo "🧹 Test file cleaned up\n";
    } else {
        echo "❌ Excel file was not created\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "📍 File: " . $e->getFile() . " Line: " . $e->getLine() . "\n";
    echo "📋 Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n🏁 Test completed!\n";
?>
