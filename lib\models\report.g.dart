// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'report.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ReportResponse _$ReportResponseFromJson(Map<String, dynamic> json) =>
    ReportResponse(
      filename: json['filename'] as String,
      downloadUrl: json['download_url'] as String,
      fileSize: json['file_size'] as String,
      generatedAt: json['generated_at'] as String,
      period: json['period'] == null
          ? null
          : ReportPeriod.fromJson(json['period'] as Map<String, dynamic>),
      userId: (json['user_id'] as num?)?.toInt(),
      siteId: (json['site_id'] as num?)?.toInt(),
      testMode: json['test_mode'] as bool?,
    );

Map<String, dynamic> _$ReportResponseToJson(ReportResponse instance) =>
    <String, dynamic>{
      'filename': instance.filename,
      'download_url': instance.downloadUrl,
      'file_size': instance.fileSize,
      'generated_at': instance.generatedAt,
      'period': instance.period,
      'user_id': instance.userId,
      'site_id': instance.siteId,
      'test_mode': instance.testMode,
    };

ReportPeriod _$ReportPeriodFromJson(Map<String, dynamic> json) => ReportPeriod(
      start: json['start'] as String,
      end: json['end'] as String,
    );

Map<String, dynamic> _$ReportPeriodToJson(ReportPeriod instance) =>
    <String, dynamic>{
      'start': instance.start,
      'end': instance.end,
    };

PresenceVerificationResponse _$PresenceVerificationResponseFromJson(
        Map<String, dynamic> json) =>
    PresenceVerificationResponse(
      isPresent: json['is_present'] as bool,
      distance: (json['distance'] as num).toDouble(),
      maxDistance: (json['max_distance'] as num).toDouble(),
      site: json['site'] == null
          ? null
          : VerificationSite.fromJson(json['site'] as Map<String, dynamic>),
      status: json['status'] as String,
      verificationTime: json['verification_time'] as String,
      message: json['message'] as String,
    );

Map<String, dynamic> _$PresenceVerificationResponseToJson(
        PresenceVerificationResponse instance) =>
    <String, dynamic>{
      'is_present': instance.isPresent,
      'distance': instance.distance,
      'max_distance': instance.maxDistance,
      'site': instance.site,
      'status': instance.status,
      'verification_time': instance.verificationTime,
      'message': instance.message,
    };

VerificationSite _$VerificationSiteFromJson(Map<String, dynamic> json) =>
    VerificationSite(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
    );

Map<String, dynamic> _$VerificationSiteToJson(VerificationSite instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
    };

AllEmployeesCheckResponse _$AllEmployeesCheckResponseFromJson(
        Map<String, dynamic> json) =>
    AllEmployeesCheckResponse(
      totalChecked: (json['total_checked'] as num).toInt(),
      presentCount: (json['present_count'] as num).toInt(),
      absentCount: (json['absent_count'] as num).toInt(),
      verifications: (json['verifications'] as List<dynamic>)
          .map((e) => EmployeeVerification.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$AllEmployeesCheckResponseToJson(
        AllEmployeesCheckResponse instance) =>
    <String, dynamic>{
      'total_checked': instance.totalChecked,
      'present_count': instance.presentCount,
      'absent_count': instance.absentCount,
      'verifications': instance.verifications,
    };

EmployeeVerification _$EmployeeVerificationFromJson(
        Map<String, dynamic> json) =>
    EmployeeVerification(
      employee: EmployeeInfo.fromJson(json['employee'] as Map<String, dynamic>),
      verification: PresenceVerificationResponse.fromJson(
          json['verification'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$EmployeeVerificationToJson(
        EmployeeVerification instance) =>
    <String, dynamic>{
      'employee': instance.employee,
      'verification': instance.verification,
    };

EmployeeInfo _$EmployeeInfoFromJson(Map<String, dynamic> json) => EmployeeInfo(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      email: json['email'] as String,
      role: json['role'] as String?,
    );

Map<String, dynamic> _$EmployeeInfoToJson(EmployeeInfo instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'email': instance.email,
      'role': instance.role,
    };

PresenceVerificationRequest _$PresenceVerificationRequestFromJson(
        Map<String, dynamic> json) =>
    PresenceVerificationRequest(
      userId: (json['user_id'] as num).toInt(),
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
    );

Map<String, dynamic> _$PresenceVerificationRequestToJson(
        PresenceVerificationRequest instance) =>
    <String, dynamic>{
      'user_id': instance.userId,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
    };
