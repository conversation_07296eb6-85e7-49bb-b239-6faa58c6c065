<?php

/**
 * ENDPOINT CORRECT POUR RÉCUPÉRER LE SITE ASSIGNÉ
 * 
 * À ajouter dans le backend ClockIn à C:\wamp64\www\clockin
 * 
 * STRUCTURE RÉELLE DE LA BASE DE DONNÉES :
 * - Table 'users' : id, name, email, role (PAS de default_site_id)
 * - Table 'sites' : id, name, latitude, longitude
 * - Table 'assignments' : id, user_id, site_id, created_at, updated_at
 * 
 * ÉTAPES D'IMPLÉMENTATION :
 * 
 * 1. Ajouter cette route dans routes/api.php :
 *    Route::middleware('auth:sanctum')->get('/sites/user-assigned', [SiteController::class, 'getUserAssigned']);
 * 
 * 2. Ajouter cette méthode dans app/Http/Controllers/SiteController.php :
 */

/**
 * Récupère le site assigné à l'utilisateur connecté via la table assignments
 * 
 * @authenticated
 * 
 * @response 200 {
 *   "success": true,
 *   "data": {
 *     "id": 5,
 *     "name": "ain soltan",
 *     "latitude": "36.13317760",
 *     "longitude": "4.73510890"
 *   }
 * }
 * 
 * @response 404 {
 *   "success": false,
 *   "message": "Aucun site assigné trouvé.",
 *   "message_ar": "لم يتم العثور على موقع مخصص."
 * }
 */
public function getUserAssigned(Request $request)
{
    try {
        $user = $request->user();
        
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'Utilisateur non authentifié.',
                'message_ar' => 'المستخدم غير مصادق عليه.'
            ], 401);
        }

        \Log::info("Getting assigned site for user: {$user->id} ({$user->email})");

        // Méthode 1: Récupérer le site via la table assignments (méthode principale)
        $assignment = \DB::table('assignments')
            ->join('sites', 'assignments.site_id', '=', 'sites.id')
            ->where('assignments.user_id', $user->id)
            ->select(
                'sites.id',
                'sites.name',
                'sites.latitude',
                'sites.longitude',
                'sites.created_at',
                'sites.updated_at'
            )
            ->first();

        if ($assignment) {
            \Log::info("Found site via assignments table: {$assignment->name} (ID: {$assignment->id})");
            
            // Convertir en objet Site pour la réponse
            $site = [
                'id' => $assignment->id,
                'name' => $assignment->name,
                'latitude' => $assignment->latitude,
                'longitude' => $assignment->longitude,
                'created_at' => $assignment->created_at,
                'updated_at' => $assignment->updated_at
            ];
            
            return response()->json([
                'success' => true,
                'data' => $site,
                'message' => 'Site assigné trouvé',
                'message_ar' => 'تم العثور على الموقع المخصص'
            ]);
        }

        // Méthode 2: Chercher dans le dernier pointage (fallback)
        try {
            $lastPointage = \DB::table('pointages')
                ->join('sites', 'pointages.site_id', '=', 'sites.id')
                ->where('pointages.user_id', $user->id)
                ->select(
                    'sites.id',
                    'sites.name',
                    'sites.latitude',
                    'sites.longitude',
                    'sites.created_at',
                    'sites.updated_at'
                )
                ->orderBy('pointages.created_at', 'desc')
                ->first();
                
            if ($lastPointage) {
                \Log::info("Found site via last pointage: {$lastPointage->name} (ID: {$lastPointage->id})");
                
                $site = [
                    'id' => $lastPointage->id,
                    'name' => $lastPointage->name,
                    'latitude' => $lastPointage->latitude,
                    'longitude' => $lastPointage->longitude,
                    'created_at' => $lastPointage->created_at,
                    'updated_at' => $lastPointage->updated_at
                ];
                
                return response()->json([
                    'success' => true,
                    'data' => $site,
                    'message' => 'Site assigné trouvé via dernier pointage',
                    'message_ar' => 'تم العثور على الموقع المخصص عبر آخر نقطة'
                ]);
            }
        } catch (\Exception $e) {
            \Log::info("No pointage history found for user {$user->id}: " . $e->getMessage());
        }

        // Aucun site assigné trouvé
        \Log::warning("No assigned site found for user {$user->id}");
        return response()->json([
            'success' => false,
            'message' => 'Aucun site assigné trouvé',
            'message_ar' => 'لم يتم العثور على موقع مخصص',
            'data' => null
        ], 404);

    } catch (\Exception $e) {
        \Log::error("Error getting user assigned site: " . $e->getMessage());
        return response()->json([
            'success' => false,
            'message' => 'Erreur lors de la récupération du site assigné',
            'message_ar' => 'خطأ في استرداد الموقع المخصص',
            'error' => $e->getMessage()
        ], 500);
    }
}

/**
 * ALTERNATIVE SIMPLE : Ajouter cette route directement dans routes/api.php
 */
/*
Route::middleware('auth:sanctum')->get('/sites/user-assigned', function (Request $request) {
    $user = $request->user();
    
    if (!$user) {
        return response()->json([
            'success' => false,
            'message_ar' => 'المستخدم غير مصادق عليه'
        ], 401);
    }
    
    // Récupérer le site via la table assignments
    $assignment = DB::table('assignments')
        ->join('sites', 'assignments.site_id', '=', 'sites.id')
        ->where('assignments.user_id', $user->id)
        ->select(
            'sites.id',
            'sites.name',
            'sites.latitude',
            'sites.longitude'
        )
        ->first();
    
    if ($assignment) {
        return response()->json([
            'success' => true,
            'data' => [
                'id' => $assignment->id,
                'name' => $assignment->name,
                'latitude' => $assignment->latitude,
                'longitude' => $assignment->longitude
            ]
        ]);
    }
    
    return response()->json([
        'success' => false,
        'message_ar' => 'لم يتم تخصيص موقع'
    ], 404);
});
*/

/**
 * INSTRUCTIONS D'IMPLÉMENTATION RAPIDE :
 * 
 * 1. Ouvrir C:\wamp64\www\clockin\routes\api.php
 * 2. Ajouter cette ligne à la fin du fichier :
 * 
 * Route::middleware('auth:sanctum')->get('/sites/user-assigned', function (Request $request) {
 *     $user = $request->user();
 *     
 *     if (!$user) {
 *         return response()->json(['success' => false, 'message_ar' => 'المستخدم غير مصادق عليه'], 401);
 *     }
 *     
 *     $assignment = DB::table('assignments')
 *         ->join('sites', 'assignments.site_id', '=', 'sites.id')
 *         ->where('assignments.user_id', $user->id)
 *         ->select('sites.id', 'sites.name', 'sites.latitude', 'sites.longitude')
 *         ->first();
 *     
 *     if ($assignment) {
 *         return response()->json([
 *             'success' => true,
 *             'data' => [
 *                 'id' => $assignment->id,
 *                 'name' => $assignment->name,
 *                 'latitude' => $assignment->latitude,
 *                 'longitude' => $assignment->longitude
 *             ]
 *         ]);
 *     }
 *     
 *     return response()->json(['success' => false, 'message_ar' => 'لم يتم تخصيص موقع'], 404);
 * });
 * 
 * 3. Sauvegarder le fichier
 * 4. Tester avec l'application Flutter
 * 
 * VÉRIFICATION AVEC POSTMAN :
 * 
 * GET http://192.168.0.50:8000/api/sites/user-assigned
 * Headers: 
 *   Authorization: Bearer {employee_token}
 *   Accept: application/json
 * 
 * Réponse attendue pour l'utilisateur ID 5 :
 * {
 *   "success": true,
 *   "data": {
 *     "id": 5,
 *     "name": "ain soltan",
 *     "latitude": "36.13317760",
 *     "longitude": "4.73510890"
 *   }
 * }
 */
