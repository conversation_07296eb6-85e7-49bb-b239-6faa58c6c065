<?php

// SOLUTION IMMÉDIATE POUR LE PROBLÈME DE SITE ASSIGNÉ
// 
// Ajouter cette route dans routes/api.php :
// Route::get('/sites/user-assigned', function (Request $request) {
//     $user = $request->user();
//     
//     if (!$user) {
//         return response()->json([
//             'success' => false,
//             'message' => 'Utilisateur non authentifié.',
//             'message_ar' => 'المستخدم غير مصادق عليه.'
//         ], 401);
//     }
//     
//     // Méthode 1: Utiliser le default_site_id
//     if ($user->default_site_id) {
//         $site = \App\Models\Site::find($user->default_site_id);
//         if ($site) {
//             return response()->json([
//                 'success' => true,
//                 'data' => $site
//             ]);
//         }
//     }
//     
//     // Méthode 2: Chercher dans les relations
//     $site = $user->sites()->first();
//     if ($site) {
//         return response()->json([
//             'success' => true,
//             'data' => $site
//         ]);
//     }
//     
//     // Méthode 3: Chercher dans les assignations
//     $assignment = \DB::table('site_assignments')
//         ->where('user_id', $user->id)
//         ->where('is_active', true)
//         ->first();
//         
//     if ($assignment) {
//         $site = \App\Models\Site::find($assignment->site_id);
//         if ($site) {
//             return response()->json([
//                 'success' => true,
//                 'data' => $site
//             ]);
//         }
//     }
//     
//     return response()->json([
//         'success' => false,
//         'message' => 'Aucun site assigné trouvé.',
//         'message_ar' => 'لم يتم العثور على موقع مخصص.'
//     ], 404);
// })->middleware('auth:sanctum');

// OU MODIFIER LA MÉTHODE getUserAssignedSite() DANS PointageController.php :

/*
private function getUserAssignedSite(int $userId): ?Site
{
    $user = User::find($userId);
    
    if (!$user) {
        return null;
    }
    
    // Méthode 1: default_site_id
    if ($user->default_site_id) {
        $site = Site::find($user->default_site_id);
        if ($site) {
            \Log::info("Found site via default_site_id: {$site->name} for user {$userId}");
            return $site;
        }
    }
    
    // Méthode 2: Relation many-to-many (si elle existe)
    try {
        $site = $user->sites()->first();
        if ($site) {
            \Log::info("Found site via relation: {$site->name} for user {$userId}");
            return $site;
        }
    } catch (\Exception $e) {
        \Log::warning("No sites relation found for user {$userId}");
    }
    
    // Méthode 3: Table d'assignation (si elle existe)
    try {
        $assignment = DB::table('site_assignments')
            ->where('user_id', $userId)
            ->where('is_active', true)
            ->first();
            
        if ($assignment) {
            $site = Site::find($assignment->site_id);
            if ($site) {
                \Log::info("Found site via assignment table: {$site->name} for user {$userId}");
                return $site;
            }
        }
    } catch (\Exception $e) {
        \Log::warning("No site_assignments table found for user {$userId}");
    }
    
    // Méthode 4: Chercher dans les pointages précédents
    try {
        $lastPointage = Pointage::where('user_id', $userId)
            ->with('site')
            ->orderBy('created_at', 'desc')
            ->first();
            
        if ($lastPointage && $lastPointage->site) {
            \Log::info("Found site via last pointage: {$lastPointage->site->name} for user {$userId}");
            return $lastPointage->site;
        }
    } catch (\Exception $e) {
        \Log::warning("Error finding site via pointage for user {$userId}: " . $e->getMessage());
    }
    
    \Log::warning("No assigned site found for user {$userId}");
    return null;
}
*/

// SOLUTION TEMPORAIRE CÔTÉ FRONTEND:
// Modifier l'endpoint dans AppConstants pour utiliser un endpoint existant
// qui retourne les informations de site assigné

?>
