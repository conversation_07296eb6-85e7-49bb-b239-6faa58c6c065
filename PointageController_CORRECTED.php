<?php

namespace App\Http\Controllers;

use App\Models\Pointage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class PointageController extends Controller
{
    public function store(Request $request)
    {
        $request->validate([
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
            'accuracy' => 'nullable|numeric',
            'timestamp' => 'nullable|integer', // NOUVEAU: Validation timestamp
            'exact_time' => 'nullable|string', // NOUVEAU: Validation exact_time
        ]);

        $user = auth()->user();
        
        // UTILISER LE TIMESTAMP EXACT ENVOYÉ PAR FLUTTER
        $exactDateTime = null;
        if ($request->has('timestamp') && $request->timestamp) {
            // Convertir le timestamp Unix en DateTime
            $exactDateTime = Carbon::createFromTimestamp($request->timestamp);
            Log::info("✅ Using exact timestamp from Flutter client: " . $exactDateTime->toDateTimeString());
            Log::info("📱 Client timestamp: " . $request->timestamp);
            Log::info("🕐 Converted to: " . $exactDateTime->format('H:i:s'));
        } else {
            // Fallback vers l'heure actuelle si pas de timestamp
            $exactDateTime = Carbon::now();
            Log::warning("⚠️ No timestamp provided, using server time: " . $exactDateTime->toDateTimeString());
        }

        // Vérifier s'il y a un pointage actif
        $activePointage = Pointage::where('user_id', $user->id)
            ->whereNull('fin_pointage')
            ->first();

        if (!$activePointage) {
            // DÉBUT DE POINTAGE - UTILISER L'HEURE EXACTE
            $pointage = Pointage::create([
                'user_id' => $user->id,
                'site_id' => $this->getUserSiteId($user),
                'debut_pointage' => $exactDateTime, // UTILISER L'HEURE EXACTE
                'debut_latitude' => $request->latitude,
                'debut_longitude' => $request->longitude,
                'created_at' => $exactDateTime, // AUSSI POUR created_at
                'updated_at' => $exactDateTime, // AUSSI POUR updated_at
            ]);

            Log::info("✅ Check-in recorded with exact time: " . $exactDateTime->toDateTimeString());
            Log::info("🎯 Time preserved from Flutter: " . $exactDateTime->format('H:i:s'));

            return response()->json([
                'success' => true,
                'message' => 'Début du pointage enregistré.',
                'message_ar' => 'تم تسجيل بداية الحضور.',
                'data' => $pointage->fresh(),
            ], 201);
        } else {
            // FIN DE POINTAGE - UTILISER L'HEURE EXACTE
            $activePointage->update([
                'fin_pointage' => $exactDateTime, // UTILISER L'HEURE EXACTE
                'fin_latitude' => $request->latitude,
                'fin_longitude' => $request->longitude,
                'updated_at' => $exactDateTime, // AUSSI POUR updated_at
            ]);

            Log::info("✅ Check-out recorded with exact time: " . $exactDateTime->toDateTimeString());
            Log::info("🎯 Time preserved from Flutter: " . $exactDateTime->format('H:i:s'));

            return response()->json([
                'success' => true,
                'message' => 'Fin du pointage enregistrée.',
                'message_ar' => 'تم تسجيل نهاية الحضور.',
                'data' => $activePointage->fresh(),
            ], 200);
        }
    }

    // Méthode helper pour récupérer le site de l'utilisateur
    private function getUserSiteId($user)
    {
        // Logique pour récupérer le site assigné à l'utilisateur
        // Peut être basé sur assignments table ou default_site_id
        return $user->default_site_id ?? 1;
    }

    // Autres méthodes du controller...
}
