<?php

namespace App\Services;

use App\Models\User;
use App\Models\Pointage;
use App\Models\Site;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

/**
 * Service de gestion des exports pour le système ClockIn
 * Version qui utilise CSV par défaut pour éviter les problèmes de mémoire
 */
class ExportService
{
    private SimpleExportService $simpleExportService;

    public function __construct()
    {
        $this->simpleExportService = new SimpleExportService();
    }

    /**
     * Génère un rapport pour tous les employés
     * Essaie Excel d'abord, puis CSV en cas d'échec
     */
    public function generateEmployeeReport(Carbon $startDate, Carbon $endDate, array $options = []): string
    {
        Log::info('ExportService: Generating employee report (Excel)', [
            'start_date' => $startDate->toDateString(),
            'end_date' => $endDate->toDateString()
        ]);

        try {
            // Essayer Excel d'abord avec des limites de mémoire optimisées
            ini_set('memory_limit', '256M');
            ini_set('max_execution_time', 180);

            $data = $this->prepareEmployeeReportData($startDate, $endDate, $options);
            $filename = 'rapport_employes_' . $startDate->format('Y-m-d') . '_' . $endDate->format('Y-m-d') . '.xlsx';

            \Maatwebsite\Excel\Facades\Excel::store(new \App\Exports\EmployeeReportExport($data), $filename, 'local');
            $filePath = storage_path('app/' . $filename);

            if (file_exists($filePath)) {
                Log::info('ExportService: Excel employee report generated successfully');
                return $filePath;
            } else {
                throw new \Exception('Excel file was not created');
            }

        } catch (\Exception $e) {
            Log::warning('ExportService: Excel generation failed, falling back to CSV', [
                'error' => $e->getMessage()
            ]);

            // Fallback vers CSV
            return $this->simpleExportService->generateEmployeeReport($startDate, $endDate, $options);
        }
    }

    /**
     * Génère un rapport pour un employé spécifique
     * Essaie Excel d'abord, puis CSV en cas d'échec
     */
    public function generateIndividualEmployeeReport(int $userId, Carbon $startDate, Carbon $endDate): string
    {
        Log::info('ExportService: Generating individual employee report (Excel)', [
            'user_id' => $userId,
            'start_date' => $startDate->toDateString(),
            'end_date' => $endDate->toDateString()
        ]);

        try {
            // Essayer Excel d'abord avec des limites de mémoire optimisées
            ini_set('memory_limit', '256M');
            ini_set('max_execution_time', 180);

            $user = User::findOrFail($userId);
            $data = $this->prepareIndividualEmployeeData($user, $startDate, $endDate);
            $filename = 'rapport_' . str_replace(' ', '_', $user->name) . '_' . $startDate->format('Y-m-d') . '_' . $endDate->format('Y-m-d') . '.xlsx';

            \Maatwebsite\Excel\Facades\Excel::store(new \App\Exports\DetailedPointagesExport($data), $filename, 'local');
            $filePath = storage_path('app/' . $filename);

            if (file_exists($filePath)) {
                Log::info('ExportService: Excel individual report generated successfully');
                return $filePath;
            } else {
                throw new \Exception('Excel file was not created');
            }

        } catch (\Exception $e) {
            Log::warning('ExportService: Excel generation failed, falling back to CSV', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            // Fallback vers CSV
            return $this->simpleExportService->generateIndividualEmployeeReport($userId, $startDate, $endDate);
        }
    }

    /**
     * Génère un rapport par site
     * Essaie Excel d'abord, puis CSV en cas d'échec
     */
    public function generateSiteReport(int $siteId, Carbon $startDate, Carbon $endDate): string
    {
        Log::info('ExportService: Generating site report (Excel)', [
            'site_id' => $siteId,
            'start_date' => $startDate->toDateString(),
            'end_date' => $endDate->toDateString()
        ]);

        try {
            // Essayer Excel d'abord avec des limites de mémoire optimisées
            ini_set('memory_limit', '256M');
            ini_set('max_execution_time', 180);

            $site = Site::findOrFail($siteId);
            $data = $this->prepareSiteReportData($site, $startDate, $endDate);
            $filename = 'rapport_site_' . str_replace(' ', '_', $site->name) . '_' . $startDate->format('Y-m-d') . '_' . $endDate->format('Y-m-d') . '.xlsx';

            \Maatwebsite\Excel\Facades\Excel::store(new \App\Exports\SiteReportExport($data), $filename, 'local');
            $filePath = storage_path('app/' . $filename);

            if (file_exists($filePath)) {
                Log::info('ExportService: Excel site report generated successfully');
                return $filePath;
            } else {
                throw new \Exception('Excel file was not created');
            }

        } catch (\Exception $e) {
            Log::warning('ExportService: Excel generation failed, falling back to CSV', [
                'site_id' => $siteId,
                'error' => $e->getMessage()
            ]);

            // Fallback vers CSV
            return $this->simpleExportService->generateSiteReport($siteId, $startDate, $endDate);
        }
    }

    /**
     * Méthode pour générer un rapport Excel (si nécessaire dans le futur)
     * Cette méthode peut être appelée explicitement quand Excel est vraiment nécessaire
     */
    public function generateExcelReport(string $type, array $params): string
    {
        // Augmenter les limites pour Excel
        ini_set('memory_limit', '1G');
        ini_set('max_execution_time', 600); // 10 minutes

        try {
            Log::info('ExportService: Attempting Excel generation', [
                'type' => $type,
                'memory_limit' => ini_get('memory_limit')
            ]);

            switch ($type) {
                case 'individual':
                    return $this->generateExcelIndividualReport($params['user_id'], $params['start_date'], $params['end_date']);
                case 'employees':
                    return $this->generateExcelEmployeeReport($params['start_date'], $params['end_date'], $params['options'] ?? []);
                case 'site':
                    return $this->generateExcelSiteReport($params['site_id'], $params['start_date'], $params['end_date']);
                default:
                    throw new \Exception('Unknown report type: ' . $type);
            }

        } catch (\Exception $e) {
            Log::error('ExportService: Excel generation failed', [
                'type' => $type,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Génère un rapport Excel pour un employé individuel
     */
    private function generateExcelIndividualReport(int $userId, Carbon $startDate, Carbon $endDate): string
    {
        $user = User::findOrFail($userId);
        $data = $this->prepareIndividualEmployeeData($user, $startDate, $endDate);

        $filename = 'rapport_' . str_replace(' ', '_', $user->name) . '_' . $startDate->format('Y-m-d') . '_' . $endDate->format('Y-m-d') . '.xlsx';

        \Maatwebsite\Excel\Facades\Excel::store(new \App\Exports\DetailedPointagesExport($data), $filename, 'local');

        return storage_path('app/' . $filename);
    }

    /**
     * Génère un rapport Excel pour tous les employés
     */
    private function generateExcelEmployeeReport(Carbon $startDate, Carbon $endDate, array $options): string
    {
        $data = $this->prepareEmployeeReportData($startDate, $endDate, $options);

        $filename = 'rapport_employes_' . $startDate->format('Y-m-d') . '_' . $endDate->format('Y-m-d') . '.xlsx';

        \Maatwebsite\Excel\Facades\Excel::store(new \App\Exports\EmployeeReportExport($data), $filename, 'local');

        return storage_path('app/' . $filename);
    }

    /**
     * Génère un rapport Excel par site
     */
    private function generateExcelSiteReport(int $siteId, Carbon $startDate, Carbon $endDate): string
    {
        $site = Site::findOrFail($siteId);
        $data = $this->prepareSiteReportData($site, $startDate, $endDate);

        $filename = 'rapport_site_' . str_replace(' ', '_', $site->name) . '_' . $startDate->format('Y-m-d') . '_' . $endDate->format('Y-m-d') . '.xlsx';

        \Maatwebsite\Excel\Facades\Excel::store(new \App\Exports\SiteReportExport($data), $filename, 'local');

        return storage_path('app/' . $filename);
    }

    /**
     * Prépare les données pour le rapport global des employés
     */
    private function prepareEmployeeReportData(Carbon $startDate, Carbon $endDate, array $options): array
    {
        $pointages = Pointage::with(['user', 'site'])
            ->whereBetween('debut_pointage', [$startDate, $endDate])
            ->orderBy('debut_pointage', 'desc')
            ->get();

        $employeeData = [];

        foreach ($pointages as $pointage) {
            $userId = $pointage->user_id;

            if (!isset($employeeData[$userId])) {
                $employeeData[$userId] = [
                    'user' => $pointage->user,
                    'pointages' => [],
                    'statistics' => [
                        'total_pointages' => 0,
                        'completed_pointages' => 0,
                        'total_hours' => 0,
                        'average_daily_hours' => 0,
                        'sites_worked' => []
                    ]
                ];
            }

            $employeeData[$userId]['pointages'][] = $pointage;
            $employeeData[$userId]['statistics']['total_pointages']++;

            if ($pointage->fin_pointage) {
                $employeeData[$userId]['statistics']['completed_pointages']++;
                $hours = Carbon::parse($pointage->debut_pointage)
                    ->diffInHours(Carbon::parse($pointage->fin_pointage));
                $employeeData[$userId]['statistics']['total_hours'] += $hours;
            }

            if (!in_array($pointage->site->name, $employeeData[$userId]['statistics']['sites_worked'])) {
                $employeeData[$userId]['statistics']['sites_worked'][] = $pointage->site->name;
            }
        }

        foreach ($employeeData as &$data) {
            $totalDays = $startDate->diffInDays($endDate) + 1;
            $data['statistics']['average_daily_hours'] = $totalDays > 0
                ? round($data['statistics']['total_hours'] / $totalDays, 2)
                : 0;
        }

        return [
            'period' => [
                'start' => $startDate,
                'end' => $endDate
            ],
            'employees' => $employeeData,
            'summary' => $this->calculateGlobalSummary($employeeData)
        ];
    }

    /**
     * Prépare les données pour un employé individuel
     */
    private function prepareIndividualEmployeeData(User $user, Carbon $startDate, Carbon $endDate): array
    {
        $pointages = Pointage::with(['site'])
            ->where('user_id', $user->id)
            ->whereBetween('debut_pointage', [$startDate, $endDate])
            ->orderBy('debut_pointage', 'desc')
            ->get();

        $statistics = [
            'total_pointages' => $pointages->count(),
            'completed_pointages' => $pointages->whereNotNull('fin_pointage')->count(),
            'active_pointages' => $pointages->whereNull('fin_pointage')->count(),
            'total_hours' => 0,
            'average_daily_hours' => 0,
            'sites_worked' => [],
            'daily_breakdown' => []
        ];

        foreach ($pointages as $pointage) {
            $date = Carbon::parse($pointage->debut_pointage)->format('Y-m-d');

            if (!isset($statistics['daily_breakdown'][$date])) {
                $statistics['daily_breakdown'][$date] = [
                    'date' => $date,
                    'pointages' => 0,
                    'hours' => 0,
                    'sites' => []
                ];
            }

            $statistics['daily_breakdown'][$date]['pointages']++;

            if ($pointage->fin_pointage) {
                $hours = Carbon::parse($pointage->debut_pointage)
                    ->diffInHours(Carbon::parse($pointage->fin_pointage));
                $statistics['total_hours'] += $hours;
                $statistics['daily_breakdown'][$date]['hours'] += $hours;
            }

            if (!in_array($pointage->site->name, $statistics['sites_worked'])) {
                $statistics['sites_worked'][] = $pointage->site->name;
            }

            if (!in_array($pointage->site->name, $statistics['daily_breakdown'][$date]['sites'])) {
                $statistics['daily_breakdown'][$date]['sites'][] = $pointage->site->name;
            }
        }

        $totalDays = $startDate->diffInDays($endDate) + 1;
        $statistics['average_daily_hours'] = $totalDays > 0
            ? round($statistics['total_hours'] / $totalDays, 2)
            : 0;

        return [
            'user' => $user,
            'period' => [
                'start' => $startDate,
                'end' => $endDate
            ],
            'pointages' => $pointages,
            'statistics' => $statistics
        ];
    }

    /**
     * Prépare les données pour un rapport de site
     */
    private function prepareSiteReportData(Site $site, Carbon $startDate, Carbon $endDate): array
    {
        $pointages = Pointage::with(['user'])
            ->where('site_id', $site->id)
            ->whereBetween('debut_pointage', [$startDate, $endDate])
            ->orderBy('debut_pointage', 'desc')
            ->get();

        return [
            'site' => $site,
            'period' => [
                'start' => $startDate,
                'end' => $endDate
            ],
            'pointages' => $pointages,
            'statistics' => [
                'total_pointages' => $pointages->count(),
                'completed_pointages' => $pointages->whereNotNull('fin_pointage')->count(),
                'total_hours' => $pointages->sum(function($p) {
                    return $p->fin_pointage ? Carbon::parse($p->debut_pointage)->diffInHours(Carbon::parse($p->fin_pointage)) : 0;
                })
            ]
        ];
    }

    /**
     * Calcule un résumé global
     */
    private function calculateGlobalSummary(array $employeeData): array
    {
        $totalEmployees = count($employeeData);
        $totalPointages = 0;
        $totalHours = 0;

        foreach ($employeeData as $data) {
            $totalPointages += $data['statistics']['total_pointages'];
            $totalHours += $data['statistics']['total_hours'];
        }

        return [
            'total_employees' => $totalEmployees,
            'total_pointages' => $totalPointages,
            'total_hours' => round($totalHours, 2),
            'average_hours_per_employee' => $totalEmployees > 0 ? round($totalHours / $totalEmployees, 2) : 0
        ];
    }
}
