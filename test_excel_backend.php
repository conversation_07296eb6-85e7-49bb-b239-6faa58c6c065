<?php

// Test the Excel generation with the modified backend
echo "🔧 Testing Excel Generation Backend\n";
echo "==================================\n\n";

try {
    // Change to the Laravel directory
    chdir('C:\wamp64\www\clockin');
    
    // Include Laravel bootstrap
    require_once 'vendor/autoload.php';
    
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "✅ Laravel bootstrapped\n";
    
    // Test the modified ExportService
    $exportService = new \App\Services\ExportService();
    echo "✅ Modified ExportService instantiated\n";
    
    // Test individual report generation
    echo "🔄 Testing individual Excel report generation...\n";
    
    $userId = 5;
    $startDate = \Carbon\Carbon::parse('2025-07-28')->startOfDay();
    $endDate = \Carbon\Carbon::parse('2025-08-04')->endOfDay();
    
    echo "📅 Date range: {$startDate->toDateString()} to {$endDate->toDateString()}\n";
    echo "👤 User ID: $userId\n";
    
    $filePath = $exportService->generateIndividualEmployeeReport($userId, $startDate, $endDate);
    
    if (file_exists($filePath)) {
        $fileSize = filesize($filePath);
        $filename = basename($filePath);
        $extension = pathinfo($filePath, PATHINFO_EXTENSION);
        
        echo "✅ Individual report generated successfully!\n";
        echo "   📁 File: $filename\n";
        echo "   📏 Size: $fileSize bytes\n";
        echo "   📄 Type: $extension\n";
        
        if ($extension === 'xlsx') {
            echo "   🎉 Excel file generated successfully!\n";
        } else {
            echo "   ⚠️ Fallback to CSV was used\n";
        }
        
        // Clean up test file
        unlink($filePath);
        echo "   🧹 Test file cleaned up\n";
    } else {
        echo "❌ Individual report generation failed - file not found\n";
    }
    
    echo "\n🔄 Testing employee Excel report generation (all employees)...\n";
    
    $filePath2 = $exportService->generateEmployeeReport($startDate, $endDate);
    
    if (file_exists($filePath2)) {
        $fileSize2 = filesize($filePath2);
        $filename2 = basename($filePath2);
        $extension2 = pathinfo($filePath2, PATHINFO_EXTENSION);
        
        echo "✅ Employee report generated successfully!\n";
        echo "   📁 File: $filename2\n";
        echo "   📏 Size: $fileSize2 bytes\n";
        echo "   📄 Type: $extension2\n";
        
        if ($extension2 === 'xlsx') {
            echo "   🎉 Excel file generated successfully!\n";
        } else {
            echo "   ⚠️ Fallback to CSV was used\n";
        }
        
        // Clean up test file
        unlink($filePath2);
        echo "   🧹 Test file cleaned up\n";
    } else {
        echo "❌ Employee report generation failed - file not found\n";
    }
    
    echo "\n🔄 Testing site Excel report generation...\n";
    
    // Find a valid site ID
    $sites = \App\Models\Site::all(['id', 'name']);
    if ($sites->count() > 0) {
        $site = $sites->first();
        echo "🏢 Using site: {$site->name} (ID: {$site->id})\n";
        
        $filePath3 = $exportService->generateSiteReport($site->id, $startDate, $endDate);
        
        if (file_exists($filePath3)) {
            $fileSize3 = filesize($filePath3);
            $filename3 = basename($filePath3);
            $extension3 = pathinfo($filePath3, PATHINFO_EXTENSION);
            
            echo "✅ Site report generated successfully!\n";
            echo "   📁 File: $filename3\n";
            echo "   📏 Size: $fileSize3 bytes\n";
            echo "   📄 Type: $extension3\n";
            
            if ($extension3 === 'xlsx') {
                echo "   🎉 Excel file generated successfully!\n";
            } else {
                echo "   ⚠️ Fallback to CSV was used\n";
            }
            
            // Clean up test file
            unlink($filePath3);
            echo "   🧹 Test file cleaned up\n";
        } else {
            echo "❌ Site report generation failed - file not found\n";
        }
    } else {
        echo "⚠️ No sites found in database - skipping site report test\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "📍 File: " . $e->getFile() . " Line: " . $e->getLine() . "\n";
    echo "📋 Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n🏁 Excel backend test completed!\n";
?>
