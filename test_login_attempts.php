<?php

// Test different login credentials
echo "🔐 Testing Login Credentials\n";
echo "===========================\n\n";

$commonPasswords = [
    'password',
    'admin',
    '123456',
    'clockin',
    'admin123',
    'password123'
];

foreach ($commonPasswords as $password) {
    echo "🔑 Trying password: $password\n";
    
    $loginData = [
        'email' => '<EMAIL>',
        'password' => $password
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://192.168.200.18:8000/api/auth/login');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($loginData));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json'
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode === 200) {
        $data = json_decode($response, true);
        if (isset($data['data']['token'])) {
            echo "✅ SUCCESS! Password is: $password\n";
            echo "🔑 Token: " . substr($data['data']['token'], 0, 20) . "...\n";
            break;
        }
    } else {
        echo "❌ Failed (HTTP $httpCode)\n";
    }
}

echo "\n🏁 Login test completed!\n";
?>
