<?php

// Test the simple export service
echo "🔧 Testing Simple Export Service\n";
echo "===============================\n\n";

try {
    // Change to the Laravel directory
    chdir('C:\wamp64\www\clockin');
    
    // Include Laravel bootstrap
    require_once 'vendor/autoload.php';
    
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "✅ Laravel bootstrapped\n";
    
    // Include our simple export service
    require_once 'SimpleExportService.php';
    
    $simpleExportService = new \App\Services\SimpleExportService();
    echo "✅ SimpleExportService created\n";
    
    // Test individual report generation
    echo "🔄 Testing individual report generation...\n";
    
    $userId = 5;
    $startDate = \Carbon\Carbon::parse('2025-07-28')->startOfDay();
    $endDate = \Carbon\Carbon::parse('2025-08-04')->endOfDay();
    
    $filePath = $simpleExportService->generateIndividualEmployeeReport($userId, $startDate, $endDate);
    
    if (file_exists($filePath)) {
        $fileSize = filesize($filePath);
        echo "✅ Individual report generated successfully!\n";
        echo "   📁 File: " . basename($filePath) . "\n";
        echo "   📏 Size: $fileSize bytes\n";
        
        // Show first few lines of the CSV
        $content = file_get_contents($filePath);
        $lines = explode("\n", $content);
        echo "   📄 First 10 lines:\n";
        for ($i = 0; $i < min(10, count($lines)); $i++) {
            echo "      " . ($i + 1) . ": " . $lines[$i] . "\n";
        }
        
        // Clean up test file
        unlink($filePath);
        echo "   🧹 Test file cleaned up\n";
    } else {
        echo "❌ Individual report generation failed\n";
    }
    
    echo "\n🔄 Testing employee report generation...\n";
    
    $filePath2 = $simpleExportService->generateEmployeeReport($startDate, $endDate);
    
    if (file_exists($filePath2)) {
        $fileSize2 = filesize($filePath2);
        echo "✅ Employee report generated successfully!\n";
        echo "   📁 File: " . basename($filePath2) . "\n";
        echo "   📏 Size: $fileSize2 bytes\n";
        
        // Clean up test file
        unlink($filePath2);
        echo "   🧹 Test file cleaned up\n";
    } else {
        echo "❌ Employee report generation failed\n";
    }
    
    echo "\n🔄 Testing site report generation...\n";
    
    $siteId = 1; // Assuming site ID 1 exists
    $filePath3 = $simpleExportService->generateSiteReport($siteId, $startDate, $endDate);
    
    if (file_exists($filePath3)) {
        $fileSize3 = filesize($filePath3);
        echo "✅ Site report generated successfully!\n";
        echo "   📁 File: " . basename($filePath3) . "\n";
        echo "   📏 Size: $fileSize3 bytes\n";
        
        // Clean up test file
        unlink($filePath3);
        echo "   🧹 Test file cleaned up\n";
    } else {
        echo "❌ Site report generation failed\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "📍 File: " . $e->getFile() . " Line: " . $e->getLine() . "\n";
    echo "📋 Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n🏁 Simple export test completed!\n";
?>
