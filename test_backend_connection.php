<?php

/**
 * Script de test pour vérifier la connexion et les endpoints du backend
 * À placer dans le dossier public du projet Laravel pour test rapide
 */

echo "🔍 Test de Connexion Backend ClockIn\n";
echo "=====================================\n\n";

// Configuration
$baseUrl = 'http://localhost/clockin/public';
$testEmail = '<EMAIL>'; // Changez selon votre configuration
$testPassword = 'password'; // Changez selon votre configuration

echo "🌐 URL de base: $baseUrl\n";
echo "👤 Email de test: $testEmail\n\n";

// Fonction pour faire des requêtes HTTP
function makeRequest($url, $method = 'GET', $data = null, $headers = []) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    
    if ($method === 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        if ($data) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
    }
    
    if (!empty($headers)) {
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    curl_close($ch);
    
    return [
        'response' => $response,
        'http_code' => $httpCode,
        'error' => $error
    ];
}

// Test 1: Vérifier que le serveur répond
echo "📡 Test 1: Connexion au serveur\n";
echo "--------------------------------\n";

$result = makeRequest($baseUrl);

if ($result['error']) {
    echo "❌ Erreur de connexion: " . $result['error'] . "\n";
    echo "💡 Vérifiez que WAMP est démarré et que l'URL est correcte\n\n";
} else {
    echo "✅ Serveur accessible (Code HTTP: " . $result['http_code'] . ")\n\n";
}

// Test 2: Test de l'endpoint d'authentification
echo "🔐 Test 2: Endpoint d'authentification\n";
echo "---------------------------------------\n";

$loginData = [
    'email' => $testEmail,
    'password' => $testPassword
];

$headers = [
    'Content-Type: application/json',
    'Accept: application/json'
];

$loginResult = makeRequest(
    $baseUrl . '/api/auth/login',
    'POST',
    $loginData,
    $headers
);

if ($loginResult['error']) {
    echo "❌ Erreur de connexion à l'API: " . $loginResult['error'] . "\n\n";
} else {
    echo "📊 Code de réponse: " . $loginResult['http_code'] . "\n";
    
    $loginResponse = json_decode($loginResult['response'], true);
    
    if ($loginResponse && isset($loginResponse['success']) && $loginResponse['success']) {
        echo "✅ Authentification réussie\n";
        $token = $loginResponse['data']['token'] ?? null;
        
        if ($token) {
            echo "🔑 Token obtenu: " . substr($token, 0, 20) . "...\n\n";
            
            // Test 3: Test de l'endpoint des rapports
            echo "📊 Test 3: Endpoint des données de présence\n";
            echo "--------------------------------------------\n";
            
            $reportsHeaders = [
                'Content-Type: application/json',
                'Accept: application/json',
                'Authorization: Bearer ' . $token
            ];
            
            $attendanceUrl = $baseUrl . '/api/reports/attendance?' . http_build_query([
                'start_date' => date('Y-m-d', strtotime('-7 days')),
                'end_date' => date('Y-m-d'),
                'type' => 'all_employees'
            ]);
            
            $attendanceResult = makeRequest($attendanceUrl, 'GET', null, $reportsHeaders);
            
            if ($attendanceResult['error']) {
                echo "❌ Erreur lors de l'accès aux données de présence: " . $attendanceResult['error'] . "\n";
            } else {
                echo "📊 Code de réponse: " . $attendanceResult['http_code'] . "\n";
                
                $attendanceResponse = json_decode($attendanceResult['response'], true);
                
                if ($attendanceResponse) {
                    if (isset($attendanceResponse['success']) && $attendanceResponse['success']) {
                        echo "✅ Données de présence récupérées avec succès\n";
                        $attendanceCount = count($attendanceResponse['data']['attendance'] ?? []);
                        echo "📈 Nombre d'enregistrements: $attendanceCount\n";
                    } else {
                        echo "⚠️ Réponse reçue mais pas de succès:\n";
                        echo json_encode($attendanceResponse, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
                    }
                } else {
                    echo "❌ Réponse invalide:\n";
                    echo $attendanceResult['response'] . "\n";
                }
            }
            
            echo "\n";
            
            // Test 4: Test de génération de rapport
            echo "📋 Test 4: Génération de rapport\n";
            echo "---------------------------------\n";
            
            $reportData = [
                'start_date' => date('Y-m-d', strtotime('-7 days')),
                'end_date' => date('Y-m-d'),
                'include_stats' => true
            ];
            
            $reportResult = makeRequest(
                $baseUrl . '/api/reports/employees',
                'POST',
                $reportData,
                $reportsHeaders
            );
            
            if ($reportResult['error']) {
                echo "❌ Erreur lors de la génération du rapport: " . $reportResult['error'] . "\n";
            } else {
                echo "📊 Code de réponse: " . $reportResult['http_code'] . "\n";
                
                $reportResponse = json_decode($reportResult['response'], true);
                
                if ($reportResponse && isset($reportResponse['success']) && $reportResponse['success']) {
                    echo "✅ Rapport généré avec succès\n";
                    echo "📄 Nom du fichier: " . ($reportResponse['data']['filename'] ?? 'N/A') . "\n";
                    echo "📏 Taille: " . ($reportResponse['data']['file_size'] ?? 'N/A') . "\n";
                } else {
                    echo "⚠️ Échec de la génération du rapport:\n";
                    echo json_encode($reportResponse, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
                }
            }
            
        } else {
            echo "❌ Token non trouvé dans la réponse\n";
        }
    } else {
        echo "❌ Échec de l'authentification:\n";
        echo $loginResult['response'] . "\n";
    }
}

echo "\n🏁 Tests terminés!\n\n";

// Résumé des vérifications
echo "📋 Résumé des vérifications nécessaires:\n";
echo "=========================================\n";
echo "1. ✓ WAMP est démarré\n";
echo "2. ✓ Le projet Laravel est dans C:\\wamp64\\www\\clockin\n";
echo "3. ✓ Les routes API sont ajoutées dans routes/api.php\n";
echo "4. ✓ Le contrôleur ReportController existe\n";
echo "5. ✓ Le trait ApiResponseTrait existe\n";
echo "6. ✓ Les modèles Pointage, User, Site existent\n";
echo "7. ✓ Le dossier storage/app/reports existe\n";
echo "8. ✓ Un utilisateur admin existe avec les bonnes credentials\n";
echo "9. ✓ Laravel Sanctum est configuré\n";
echo "10. ✓ La base de données contient des données de pointage\n\n";

echo "💡 Si des tests échouent, consultez INSTALLATION_BACKEND_RAPPORTS.md\n";

?>
