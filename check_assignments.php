<?php

// Script pour vérifier et corriger les assignations
require_once 'C:/wamp64/www/clockin/vendor/autoload.php';
$app = require_once 'C:/wamp64/www/clockin/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;

echo "=== VÉRIFICATION ET CORRECTION DES ASSIGNATIONS ===\n\n";

try {
    // 1. Vérifier les assignations actuelles
    echo "1. ASSIGNATIONS ACTUELLES:\n";
    echo str_repeat("-", 50) . "\n";
    
    $assignments = DB::table('assignments')
        ->join('users', 'assignments.user_id', '=', 'users.id')
        ->join('sites', 'assignments.site_id', '=', 'sites.id')
        ->select(
            'assignments.id',
            'assignments.user_id',
            'assignments.site_id',
            'users.name as user_name',
            'users.email as user_email',
            'sites.name as site_name'
        )
        ->get();
    
    if ($assignments->count() > 0) {
        foreach ($assignments as $assignment) {
            echo sprintf(
                "ID: %d | User: %s (%s) → Site: %s\n",
                $assignment->id,
                $assignment->user_name,
                $assignment->user_email,
                $assignment->site_name
            );
        }
    } else {
        echo "❌ Aucune assignation trouvée!\n";
    }
    
    // 2. Vérifier spécifiquement l'utilisateur ID 5
    echo "\n2. VÉRIFICATION UTILISATEUR ID 5:\n";
    echo str_repeat("-", 50) . "\n";
    
    $user5Assignment = DB::table('assignments')
        ->join('users', 'assignments.user_id', '=', 'users.id')
        ->join('sites', 'assignments.site_id', '=', 'sites.id')
        ->where('assignments.user_id', 5)
        ->select(
            'assignments.id',
            'users.name as user_name',
            'users.email as user_email',
            'sites.id as site_id',
            'sites.name as site_name',
            'sites.latitude',
            'sites.longitude'
        )
        ->first();
    
    if ($user5Assignment) {
        echo "✅ Utilisateur ID 5 est assigné:\n";
        echo sprintf(
            "   User: %s (%s)\n",
            $user5Assignment->user_name,
            $user5Assignment->user_email
        );
        echo sprintf(
            "   Site: %s (ID: %d)\n",
            $user5Assignment->site_name,
            $user5Assignment->site_id
        );
        echo sprintf(
            "   Coordonnées: %s, %s\n",
            $user5Assignment->latitude,
            $user5Assignment->longitude
        );
    } else {
        echo "❌ Utilisateur ID 5 n'est PAS assigné à un site!\n";
        
        // 3. Assigner automatiquement l'utilisateur ID 5 au premier site disponible
        echo "\n3. CORRECTION AUTOMATIQUE:\n";
        echo str_repeat("-", 50) . "\n";
        
        $firstSite = DB::table('sites')->first();
        if ($firstSite) {
            echo "🔧 Assignation de l'utilisateur ID 5 au site: {$firstSite->name}\n";
            
            // Vérifier si l'assignation existe déjà
            $existingAssignment = DB::table('assignments')
                ->where('user_id', 5)
                ->first();
            
            if ($existingAssignment) {
                // Mettre à jour l'assignation existante
                DB::table('assignments')
                    ->where('user_id', 5)
                    ->update([
                        'site_id' => $firstSite->id,
                        'updated_at' => now()
                    ]);
                echo "✅ Assignation mise à jour!\n";
            } else {
                // Créer une nouvelle assignation
                DB::table('assignments')->insert([
                    'user_id' => 5,
                    'site_id' => $firstSite->id,
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
                echo "✅ Nouvelle assignation créée!\n";
            }
            
            echo sprintf(
                "   Utilisateur: سماح زهراوي (<EMAIL>)\n"
            );
            echo sprintf(
                "   Site assigné: %s (ID: %d)\n",
                $firstSite->name,
                $firstSite->id
            );
            echo sprintf(
                "   Coordonnées: %s, %s\n",
                $firstSite->latitude,
                $firstSite->longitude
            );
        } else {
            echo "❌ Aucun site disponible pour l'assignation!\n";
        }
    }
    
    // 4. Vérifier toutes les assignations après correction
    echo "\n4. ASSIGNATIONS APRÈS CORRECTION:\n";
    echo str_repeat("-", 50) . "\n";
    
    $allAssignments = DB::table('assignments')
        ->join('users', 'assignments.user_id', '=', 'users.id')
        ->join('sites', 'assignments.site_id', '=', 'sites.id')
        ->select(
            'assignments.id',
            'assignments.user_id',
            'assignments.site_id',
            'users.name as user_name',
            'users.email as user_email',
            'sites.name as site_name'
        )
        ->get();
    
    foreach ($allAssignments as $assignment) {
        $highlight = ($assignment->user_id == 5) ? "🎯 " : "   ";
        echo sprintf(
            "%sID: %d | User: %s (%s) → Site: %s\n",
            $highlight,
            $assignment->id,
            $assignment->user_name,
            $assignment->user_email,
            $assignment->site_name
        );
    }
    
    // 5. Instructions pour le frontend
    echo "\n5. INSTRUCTIONS FRONTEND:\n";
    echo str_repeat("-", 50) . "\n";
    echo "✅ Le problème est maintenant corrigé côté base de données.\n";
    echo "✅ L'utilisateur ID 5 a maintenant un site assigné.\n";
    echo "\n🔧 Le frontend doit utiliser la table 'assignments' au lieu de 'default_site_id'.\n";
    echo "\n📱 Redémarrez l'application Flutter pour tester la correction.\n";
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "CORRECTION TERMINÉE\n";
echo str_repeat("=", 60) . "\n";
