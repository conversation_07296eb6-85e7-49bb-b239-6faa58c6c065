import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import '../models/models.dart';
import '../services/services.dart';
import '../constants/app_constants.dart';

enum AttendanceState {
  initial,
  loading,
  loaded,
  error,
  checkingLocation,
  processingPointage,
}

class AttendanceProvider extends ChangeNotifier {
  final ApiService _apiService = ApiService();
  final LocationService _locationService = LocationService();
  final StorageService _storageService = StorageService();

  AttendanceState _state = AttendanceState.initial;
  List<Pointage> _pointages = [];
  Pointage? _activePointage;
  PointageStats? _stats;
  Position? _currentPosition;
  Site? _assignedSite;
  LocationCheckResult? _lastLocationCheck;
  String? _errorMessage;
  bool _isLoading = false;
  int _currentPage = 1;
  bool _hasMoreData = true;

  // Getters
  AttendanceState get state => _state;
  List<Pointage> get pointages => _pointages;
  Pointage? get activePointage => _activePointage;
  PointageStats? get stats => _stats;
  Position? get currentPosition => _currentPosition;
  Site? get assignedSite => _assignedSite;
  LocationCheckResult? get lastLocationCheck => _lastLocationCheck;
  String? get errorMessage => _errorMessage;
  bool get isLoading => _isLoading;
  bool get hasActivePointage => _activePointage != null;
  bool get hasMoreData => _hasMoreData;
  int get currentPage => _currentPage;

  // Initialize
  Future<void> initialize() async {
    _setState(AttendanceState.loading);
    
    try {
      await _loadCachedData();
      await refreshData();
    } catch (e) {
      debugPrint('Attendance Provider: Initialization error: $e');
      _setError('خطأ في تهيئة بيانات الحضور');
    }
  }

  // Load cached data
  Future<void> _loadCachedData() async {
    try {
      final cachedPointages = _storageService.getAttendanceData('recent_pointages');
      if (cachedPointages.isNotEmpty) {
        _pointages = cachedPointages;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Attendance Provider: Error loading cached data: $e');
    }
  }

  // Refresh all data
  Future<void> refreshData() async {
    _setLoading(true);
    _clearError();

    try {
      await Future.wait([
        _loadActivePointage(),
        _loadPointages(refresh: true),
        _loadStats(),
      ]);
      
      _setState(AttendanceState.loaded);
    } catch (e) {
      debugPrint('Attendance Provider: Error refreshing data: $e');
      _setError('فشل في تحديث البيانات');
    } finally {
      _setLoading(false);
    }
  }

  // Load active pointage
  Future<void> _loadActivePointage() async {
    try {
      _activePointage = await _apiService.getActivePointage();
      debugPrint('Attendance Provider: Active pointage loaded');
    } catch (e) {
      debugPrint('Attendance Provider: Error loading active pointage: $e');
      _activePointage = null;
    }
  }

  // Load pointages
  Future<void> _loadPointages({bool refresh = false}) async {
    try {
      if (refresh) {
        _currentPage = 1;
        _hasMoreData = true;
        _pointages.clear();
      }

      final response = await _apiService.getPointages(
        page: _currentPage,
        perPage: AppConstants.defaultPageSize,
      );

      if (refresh) {
        _pointages = response.data;
      } else {
        _pointages.addAll(response.data);
      }

      _hasMoreData = response.hasNextPage;
      
      // Cache recent pointages
      await _storageService.saveAttendanceData('recent_pointages', _pointages);
      
      debugPrint('Attendance Provider: Pointages loaded (page $_currentPage)');
    } catch (e) {
      debugPrint('Attendance Provider: Error loading pointages: $e');
      rethrow;
    }
  }

  // Load more pointages
  Future<void> loadMorePointages() async {
    if (!_hasMoreData || _isLoading) return;

    _setLoading(true);
    
    try {
      _currentPage++;
      await _loadPointages();
    } catch (e) {
      debugPrint('Attendance Provider: Error loading more pointages: $e');
      _currentPage--; // Revert page increment
      _setError('فشل في تحميل المزيد من البيانات');
    } finally {
      _setLoading(false);
    }
  }

  // Load statistics
  Future<void> _loadStats() async {
    try {
      _stats = await _apiService.getPointageStats();
      debugPrint('Attendance Provider: Stats loaded');
    } catch (e) {
      debugPrint('Attendance Provider: Error loading stats: $e');
      _stats = null;
    }
  }

  // Check location for pointage
  Future<LocationCheckResult?> checkLocation(Site site) async {
    _setState(AttendanceState.checkingLocation);
    _clearError();

    try {
      _lastLocationCheck = await _locationService.checkLocationForPointage(site);
      _currentPosition = _lastLocationCheck!.position;
      _assignedSite = site;
      
      debugPrint('Attendance Provider: Location checked - ${_lastLocationCheck!.statusText}');
      return _lastLocationCheck;
    } catch (e) {
      debugPrint('Attendance Provider: Error checking location: $e');
      if (e is LocationException) {
        _setError(e.message);
      } else {
        _setError('فشل في تحديد الموقع');
      }
      return null;
    } finally {
      _setState(AttendanceState.loaded);
    }
  }

  // Create pointage (check-in or check-out)
  Future<bool> createPointage() async {
    if (_currentPosition == null) {
      _setError('يجب تحديد الموقع أولاً');
      return false;
    }

    _setState(AttendanceState.processingPointage);
    _clearError();

    try {
      // Utiliser l'heure locale (interprétée comme heure Algérie par le backend)
      final localTime = DateTime.now();

      final request = PointageCreateRequest(
        latitude: _currentPosition!.latitude,
        longitude: _currentPosition!.longitude,
        timestamp: localTime.millisecondsSinceEpoch ~/ 1000, // Obligatoire
        accuracy: _currentPosition!.accuracy,
        exactTime: localTime.toIso8601String(), // Heure exacte pour vérification
      );

      final response = await _apiService.savePointage(request);
      
      // Update active pointage
      if (response.isCheckIn) {
        _activePointage = response.pointage;
      } else {
        _activePointage = null;
      }

      // Refresh data
      await _loadPointages(refresh: true);
      await _loadStats();
      
      debugPrint('Attendance Provider: Pointage created - ${response.type}');
      return true;
    } catch (e) {
      debugPrint('Attendance Provider: Error creating pointage: $e');
      if (e is ApiException) {
        _setError(e.message);
      } else {
        _setError('فشل في تسجيل الحضور');
      }
      return false;
    } finally {
      _setState(AttendanceState.loaded);
    }
  }

  // Get current location
  Future<Position?> getCurrentLocation() async {
    _setState(AttendanceState.checkingLocation);
    _clearError();

    try {
      _currentPosition = await _locationService.getCurrentPosition();
      debugPrint('Attendance Provider: Current location obtained');
      return _currentPosition;
    } catch (e) {
      debugPrint('Attendance Provider: Error getting location: $e');
      if (e is LocationException) {
        _setError(e.message);
      } else {
        _setError('فشل في تحديد الموقع');
      }
      return null;
    } finally {
      _setState(AttendanceState.loaded);
    }
  }

  // Check if user can perform pointage
  bool canPerformPointage() {
    return _lastLocationCheck?.inRange ?? false;
  }

  // Check if user can perform pointage with current location
  bool canPerformPointageAt(double latitude, double longitude) {
    // This would need access to SitesProvider through dependency injection
    // For now, return true - this should be implemented properly in production
    return true;
  }

  // Get next action text
  String getNextActionText() {
    if (hasActivePointage) {
      return 'تسجيل الانصراف';
    } else {
      return 'تسجيل الحضور';
    }
  }

  // Get status text
  String getStatusText() {
    if (hasActivePointage) {
      return 'حاضر';
    } else {
      return 'غير حاضر';
    }
  }

  // Get working hours today
  String getWorkingHoursToday() {
    if (_stats != null) {
      // Calculate today's hours from stats or active pointage
      if (hasActivePointage) {
        final startTime = _activePointage!.debutPointage;
        final duration = DateTime.now().difference(startTime);
        final hours = duration.inMinutes / 60.0;
        return '${hours.toStringAsFixed(1)} ساعة';
      }
    }
    return '0 ساعة';
  }

  // Filter pointages by date range
  Future<void> filterPointages({
    String? dateFrom,
    String? dateTo,
    String? status,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      _currentPage = 1;
      _hasMoreData = true;
      _pointages.clear();

      final response = await _apiService.getPointages(
        page: _currentPage,
        perPage: AppConstants.defaultPageSize,
        dateFrom: dateFrom,
        dateTo: dateTo,
        status: status,
      );

      _pointages = response.data;
      _hasMoreData = response.hasNextPage;
      
      debugPrint('Attendance Provider: Pointages filtered');
    } catch (e) {
      debugPrint('Attendance Provider: Error filtering pointages: $e');
      _setError('فشل في تصفية البيانات');
    } finally {
      _setLoading(false);
    }
  }

  // Save offline pointage
  Future<void> saveOfflinePointage() async {
    if (_currentPosition == null) return;

    try {
      final pointageData = {
        'latitude': _currentPosition!.latitude,
        'longitude': _currentPosition!.longitude,
        'accuracy': _currentPosition!.accuracy,
        'timestamp': DateTime.now().millisecondsSinceEpoch ~/ 1000,
        'created_at': DateTime.now().toIso8601String(),
      };

      await _storageService.saveOfflinePointage(pointageData);
      debugPrint('Attendance Provider: Offline pointage saved');
    } catch (e) {
      debugPrint('Attendance Provider: Error saving offline pointage: $e');
    }
  }

  // Sync offline pointages
  Future<void> syncOfflinePointages() async {
    try {
      final offlinePointages = _storageService.getOfflinePointages();
      
      for (final pointageData in offlinePointages) {
        try {
          final request = PointageCreateRequest(
            latitude: pointageData['latitude'],
            longitude: pointageData['longitude'],
            accuracy: pointageData['accuracy'],
            timestamp: pointageData['timestamp'],
          );

          await _apiService.savePointage(request);
          debugPrint('Attendance Provider: Offline pointage synced');
        } catch (e) {
          debugPrint('Attendance Provider: Error syncing offline pointage: $e');
        }
      }

      await _storageService.clearOfflinePointages();
      await refreshData();
    } catch (e) {
      debugPrint('Attendance Provider: Error syncing offline pointages: $e');
    }
  }

  // Private helper methods
  void _setState(AttendanceState newState) {
    _state = newState;
    notifyListeners();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    _state = AttendanceState.error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    if (_state == AttendanceState.error) {
      _state = AttendanceState.loaded;
      notifyListeners();
    }
  }

  // Load attendance data
  Future<void> loadAttendanceData() async {
    await _loadPointages(refresh: true);
  }

  // Get current pointage
  Pointage? get currentPointage {
    try {
      return _pointages.where((p) => p.finPointage == null).first;
    } catch (e) {
      return null;
    }
  }

  // Get today's attendance count
  int get todayAttendanceCount {
    final today = DateTime.now();
    return _pointages.where((p) {
      final pointageDate = p.debutPointage;
      return pointageDate.year == today.year &&
             pointageDate.month == today.month &&
             pointageDate.day == today.day;
    }).length;
  }

  // Get active attendance count
  int get activeAttendanceCount {
    return _pointages.where((p) => p.finPointage == null).length;
  }

  // Get today's working hours
  String get todayWorkingHours {
    final today = DateTime.now();
    final todayPointages = _pointages.where((p) {
      final pointageDate = p.debutPointage;
      return pointageDate.year == today.year &&
             pointageDate.month == today.month &&
             pointageDate.day == today.day;
    }).toList();

    double totalHours = 0;
    for (final pointage in todayPointages) {
      final start = pointage.debutPointage;
      final end = pointage.finPointage ?? DateTime.now();
      final duration = end.difference(start);
      totalHours += duration.inMinutes / 60.0;
    }

    final hours = totalHours.floor();
    final minutes = ((totalHours - hours) * 60).round();
    return '${hours}س ${minutes}د';
  }

  // Check in method
  Future<void> checkIn(double latitude, double longitude, double accuracy, [int? siteId]) async {
    try {
      _state = AttendanceState.loading;
      notifyListeners();

      // TODO: Implement actual API call
      await Future.delayed(const Duration(seconds: 2)); // Simulate API call

      // Create new pointage
      final newPointage = Pointage(
        id: DateTime.now().millisecondsSinceEpoch,
        userId: 1, // TODO: Get from auth provider
        siteId: siteId ?? 1,
        debutPointage: DateTime.now(),
        debutLatitude: latitude.toString(),
        debutLongitude: longitude.toString(),
        createdAt: DateTime.now().toIso8601String(),
      );

      _pointages.insert(0, newPointage);
      _state = AttendanceState.loaded;
      notifyListeners();
    } catch (e) {
      _errorMessage = e.toString();
      _state = AttendanceState.error;
      notifyListeners();
      rethrow;
    }
  }

  // Check out method
  Future<void> checkOut(double latitude, double longitude, double accuracy, [String? notes]) async {
    try {
      _state = AttendanceState.loading;
      notifyListeners();

      // TODO: Implement actual API call
      await Future.delayed(const Duration(seconds: 2)); // Simulate API call

      // Update active pointage
      final activeIndex = _pointages.indexWhere((p) => p.finPointage == null);
      if (activeIndex != -1) {
        final updatedPointage = _pointages[activeIndex].copyWith(
          finPointage: DateTime.now(),
          finLatitude: latitude.toString(),
          finLongitude: longitude.toString(),
        );
        _pointages[activeIndex] = updatedPointage;
      }

      _state = AttendanceState.loaded;
      notifyListeners();
    } catch (e) {
      _errorMessage = e.toString();
      _state = AttendanceState.error;
      notifyListeners();
      rethrow;
    }
  }

  // Enhanced check in method for employees
  Future<bool> checkInEmployee({
    required int siteId,
    required double latitude,
    required double longitude,
  }) async {
    _setState(AttendanceState.processingPointage);
    _clearError();

    try {
      final request = CheckInRequest(
        siteId: siteId,
        latitude: latitude,
        longitude: longitude,
      );

      final response = await _apiService.checkIn(request);

      // Update active pointage
      _activePointage = response.pointage;

      // Refresh data
      await _loadPointages(refresh: true);
      await _loadStats();

      _setState(AttendanceState.loaded);
      debugPrint('Attendance Provider: Check-in successful');
      return true;
    } catch (e) {
      debugPrint('Attendance Provider: Error during check-in: $e');
      if (e is ApiException) {
        _setError(e.message);
      } else {
        _setError('فشل في تسجيل الحضور');
      }
      return false;
    }
  }

  // Enhanced check out method for employees
  Future<bool> checkOutEmployee({
    required double latitude,
    required double longitude,
  }) async {
    _setState(AttendanceState.processingPointage);
    _clearError();

    try {
      final request = CheckOutRequest(
        latitude: latitude,
        longitude: longitude,
      );

      await _apiService.checkOut(request);

      // Clear active pointage
      _activePointage = null;

      // Refresh data
      await _loadPointages(refresh: true);
      await _loadStats();

      _setState(AttendanceState.loaded);
      debugPrint('Attendance Provider: Check-out successful');
      return true;
    } catch (e) {
      debugPrint('Attendance Provider: Error during check-out: $e');
      if (e is ApiException) {
        _setError(e.message);
      } else {
        _setError('فشل في تسجيل الانصراف');
      }
      return false;
    }
  }

  // Load pointages for admin with filters
  Future<void> loadPointagesWithFilters({
    String? userId,
    String? siteId,
    DateTime? dateFrom,
    DateTime? dateTo,
    bool activeOnly = false,
  }) async {
    _setState(AttendanceState.loading);
    _clearError();

    try {
      _currentPage = 1;
      _hasMoreData = true;
      _pointages.clear();

      final params = <String, dynamic>{
        'page': _currentPage,
        'per_page': 20,
      };

      if (userId != null) params['user_id'] = userId;
      if (siteId != null) params['site_id'] = siteId;
      if (dateFrom != null) params['date_from'] = dateFrom.toIso8601String().split('T')[0];
      if (dateTo != null) params['date_to'] = dateTo.toIso8601String().split('T')[0];
      if (activeOnly) params['active_only'] = '1';

      final response = await _apiService.getPointages(
        page: _currentPage,
        perPage: 20,
        userId: userId != null ? int.tryParse(userId) : null,
        siteId: siteId != null ? int.tryParse(siteId) : null,
        dateFrom: params['date_from'],
        dateTo: params['date_to'],
        status: activeOnly ? 'active' : null,
      );

      _pointages = response.data;
      _hasMoreData = response.hasNextPage;

      _setState(AttendanceState.loaded);
      debugPrint('Attendance Provider: Pointages loaded - ${_pointages.length} items');
    } catch (e) {
      debugPrint('Attendance Provider: Error loading pointages: $e');
      if (e is ApiException) {
        _setError(e.message);
      } else {
        _setError('فشل في تحميل سجلات الحضور');
      }
    }
  }

  // Load current attendance for employee
  Future<void> loadCurrentAttendance() async {
    _setState(AttendanceState.loading);
    _clearError();

    try {
      await _loadActivePointage();
      _setState(AttendanceState.loaded);
      debugPrint('Attendance Provider: Current attendance loaded');
    } catch (e) {
      debugPrint('Attendance Provider: Error loading current attendance: $e');
      if (e is ApiException) {
        _setError(e.message);
      } else {
        _setError('فشل في تحميل حالة الحضور الحالية');
      }
    }
  }

  // Get current attendance
  Pointage? get currentAttendance => _activePointage;

  // Get pointages stats for admin
  Map<String, dynamic> getPointagesStats() {
    final total = _pointages.length;
    final active = _pointages.where((p) => p.isActive).length;
    final completedToday = _pointages.where((p) =>
      p.isCompleted &&
      p.debutPointage.day == DateTime.now().day &&
      p.debutPointage.month == DateTime.now().month &&
      p.debutPointage.year == DateTime.now().year
    ).length;

    return {
      'total': total,
      'active': active,
      'completed_today': completedToday,
    };
  }

  // Get today stats for employee
  Map<String, dynamic> getTodayStats() {
    final today = DateTime.now();
    final todayPointages = _pointages.where((p) =>
      p.debutPointage.day == today.day &&
      p.debutPointage.month == today.month &&
      p.debutPointage.year == today.year
    ).toList();

    final sessions = todayPointages.length;
    double totalHours = 0;

    for (final pointage in todayPointages) {
      if (pointage.duree != null) {
        // Parse duration string (HH:MM:SS format)
        final parts = pointage.duree!.split(':');
        if (parts.length >= 2) {
          final hours = int.tryParse(parts[0]) ?? 0;
          final minutes = int.tryParse(parts[1]) ?? 0;
          totalHours += hours + (minutes / 60);
        }
      }
    }

    final hoursText = '${totalHours.toStringAsFixed(1)}س';

    return {
      'sessions': sessions,
      'total_hours': hoursText,
    };
  }


}
