<?php

// Test script to debug the report generation API
echo "🔍 Testing Report Generation API\n";
echo "================================\n\n";

// Test the individual report endpoint that's failing
$url = 'http://**************:8000/api/reports/employees/5';
$data = [
    'start_date' => '2025-07-28',
    'end_date' => '2025-08-04'
];

// You'll need to get a valid token from your app
$token = 'YOUR_AUTH_TOKEN_HERE'; // Replace with actual token

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json',
    'Authorization: Bearer ' . $token
]);
curl_setopt($ch, CURLOPT_VERBOSE, true);

echo "📡 Making request to: $url\n";
echo "📦 Data: " . json_encode($data) . "\n\n";

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);

curl_close($ch);

echo "📊 Response Code: $httpCode\n";
if ($error) {
    echo "❌ cURL Error: $error\n";
}

echo "📄 Response Body:\n";
echo $response . "\n\n";

// Also test if we can access the ExportService directly
echo "🔧 Testing ExportService directly...\n";

try {
    // Change to the Laravel directory
    chdir('C:\wamp64\www\clockin');
    
    // Include Laravel bootstrap
    require_once 'vendor/autoload.php';
    
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    // Test the ExportService
    $exportService = new \App\Services\ExportService();
    
    echo "✅ ExportService instantiated successfully\n";
    
    // Test with a simple date range
    $startDate = \Carbon\Carbon::parse('2025-07-28')->startOfDay();
    $endDate = \Carbon\Carbon::parse('2025-08-04')->endOfDay();
    
    echo "📅 Testing date range: {$startDate} to {$endDate}\n";
    
    // Check if user exists
    $user = \App\Models\User::find(5);
    if ($user) {
        echo "👤 User found: {$user->name}\n";
        
        // Try to generate the report
        $filePath = $exportService->generateIndividualEmployeeReport(5, $startDate, $endDate);
        echo "📁 Report generated: $filePath\n";
        
        if (file_exists($filePath)) {
            echo "✅ File exists and size: " . filesize($filePath) . " bytes\n";
        } else {
            echo "❌ File was not created\n";
        }
    } else {
        echo "❌ User with ID 5 not found\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error testing ExportService: " . $e->getMessage() . "\n";
    echo "📍 File: " . $e->getFile() . " Line: " . $e->getLine() . "\n";
    echo "📋 Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n🏁 Test completed!\n";
?>
