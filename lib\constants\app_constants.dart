class AppConstants {
  // App Information
  static const String appName = 'ClockIn';
  static const String appDescription = 'نظام الحضور والانصراف';
  static const String appVersion = '1.0.0';

  // API Configuration
  static const String baseUrl = 'http://************:8000';
  static const String loginEndpoint = '/api/auth/login';
  static const String logoutEndpoint = '/api/auth/logout';
  static const String userEndpoint = '/api/auth/user';
  static const String sitesEndpoint = '/api/sites';
  static const String employeesEndpoint = '/api/employees';
  static const String pointageEndpoint = '/api/pointage';
  static const String checkLocationEndpoint = '/api/pointage/check-location';
  static const String userAssignedSiteEndpoint = '/api/sites/user-assigned';
  static const String monitoringEndpoint = '/api/monitoring';
  static const String reportsEndpoint = '/api/reports';
  static const String verificationEndpoint = '/verification';

  // Storage Keys
  static const String tokenKey = 'auth_token';
  static const String userKey = 'user_data';
  static const String settingsKey = 'app_settings';
  static const String cacheKey = 'app_cache';

  // Location Configuration
  static const double maxAllowedDistance = 50.0; // meters
  static const double gpsAccuracyThreshold = 100.0; // meters
  static const int locationTimeoutSeconds = 10;

  // UI Configuration
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 12.0;

  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);

  // Network Configuration
  static const int connectionTimeout = 30000; // milliseconds
  static const int receiveTimeout = 30000; // milliseconds
  static const int sendTimeout = 30000; // milliseconds

  // Cache Configuration
  static const Duration cacheExpiration = Duration(hours: 1);
  static const Duration tokenExpiration = Duration(hours: 24);

  // Validation Rules
  static const int minPasswordLength = 6;
  static const int maxNameLength = 50;
  static const int maxEmailLength = 100;

  // Date Formats
  static const String dateFormat = 'yyyy-MM-dd';
  static const String timeFormat = 'HH:mm';
  static const String dateTimeFormat = 'yyyy-MM-dd HH:mm:ss';
  static const String displayDateFormat = 'dd/MM/yyyy';
  static const String displayTimeFormat = 'hh:mm a';
  static const String displayDateTimeFormat = 'dd/MM/yyyy hh:mm a';

  // Error Messages
  static const String networkErrorMessage = 'خطأ في الاتصال بالشبكة';
  static const String serverErrorMessage = 'خطأ في الخادم';
  static const String unknownErrorMessage = 'حدث خطأ غير متوقع';
  static const String timeoutErrorMessage = 'انتهت مهلة الاتصال';
  static const String unauthorizedErrorMessage = 'غير مخول للوصول';
  static const String forbiddenErrorMessage = 'ممنوع الوصول';
  static const String notFoundErrorMessage = 'المورد غير موجود';

  // Success Messages
  static const String loginSuccessMessage = 'تم تسجيل الدخول بنجاح';
  static const String logoutSuccessMessage = 'تم تسجيل الخروج بنجاح';
  static const String checkInSuccessMessage = 'تم تسجيل الحضور بنجاح';
  static const String checkOutSuccessMessage = 'تم تسجيل الانصراف بنجاح';
  static const String updateSuccessMessage = 'تم التحديث بنجاح';
  static const String deleteSuccessMessage = 'تم الحذف بنجاح';
  static const String saveSuccessMessage = 'تم الحفظ بنجاح';

  // User Roles
  static const String adminRole = 'admin';
  static const String employeeRole = 'employee';

  // Attendance Status
  static const String activeStatus = 'active';
  static const String completedStatus = 'completed';
  static const String presentStatus = 'present';
  static const String absentStatus = 'absent';

  static const String companyName = 'ClockIn Solutions';

  // Hive Box Names
  static const String userBox = 'user_box';
  static const String settingsBox = 'settings_box';
  static const String cacheBox = 'cache_box';
  static const String attendanceBox = 'attendance_box';

  // Notification Configuration
  static const String notificationChannelId = 'clockin_notifications';
  static const String notificationChannelName = 'ClockIn Notifications';
  static const String notificationChannelDescription =
      'Notifications for ClockIn app';

  // Location Update Intervals
  static const Duration locationUpdateInterval = Duration(minutes: 5);
  static const Duration backgroundLocationInterval = Duration(minutes: 15);

  // Retry Configuration
  static const int maxRetryAttempts = 3;
  static const Duration retryDelay = Duration(seconds: 2);

  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 50;

  // Map Configuration
  static const double defaultZoom = 16.0;
  static const double maxZoom = 20.0;
  static const double minZoom = 10.0;

  // Development Configuration
  static const bool debugMode = true;
  static const bool enableLogging = true;
}
