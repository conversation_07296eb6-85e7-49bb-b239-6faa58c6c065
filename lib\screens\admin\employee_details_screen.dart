import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../providers/providers.dart';
import '../../models/models.dart';
import '../../widgets/common/custom_error_widget.dart';
import '../../routes/app_routes.dart';

class EmployeeDetailsScreen extends StatefulWidget {
  final int employeeId;

  const EmployeeDetailsScreen({super.key, required this.employeeId});

  @override
  State<EmployeeDetailsScreen> createState() => _EmployeeDetailsScreenState();
}

class _EmployeeDetailsScreenState extends State<EmployeeDetailsScreen> {
  User? _employee;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadEmployeeDetails();
  }

  void _loadEmployeeDetails() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Find employee in the provider
      final employeesProvider = context.read<EmployeesProvider>();
      await employeesProvider.loadEmployeesData();
      
      User? employee;
      try {
        employee = employeesProvider.employees
            .where((e) => e.id == widget.employeeId)
            .first;
      } catch (e) {
        employee = null;
      }
      
      setState(() {
        _employee = employee;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_employee?.name ?? 'تفاصيل الموظف'),
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: AppColors.textWhite,
        actions: [
          if (_employee != null)
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () => Navigator.pushNamed(
                context,
                AppRoutes.editEmployee,
                arguments: {'employee': _employee},
              ),
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _employee == null
              ? CustomErrorWidget(
                  message: 'لم يتم العثور على الموظف',
                  onRetry: _loadEmployeeDetails,
                )
              : _buildEmployeeDetails(),
    );
  }

  Widget _buildEmployeeDetails() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        children: [
          _buildEmployeeHeader(),
          const SizedBox(height: AppConstants.defaultPadding),
          _buildPersonalInfo(),
          const SizedBox(height: AppConstants.defaultPadding),
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildEmployeeHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.largePadding),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.primaryBlue, AppColors.primaryBlueLight],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryBlue.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          CircleAvatar(
            radius: 50,
            backgroundColor: AppColors.textWhite.withValues(alpha: 0.2),
            child: Text(
              _employee!.name.isNotEmpty ? _employee!.name[0].toUpperCase() : 'U',
              style: const TextStyle(
                color: AppColors.textWhite,
                fontSize: 36,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            _employee!.name,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: AppColors.textWhite,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            _employee!.email,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textWhite.withValues(alpha: 0.9),
            ),
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: AppColors.textWhite.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              _getRoleText(_employee!.role),
              style: const TextStyle(
                color: AppColors.textWhite,
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPersonalInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات الملف الشخصي',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.primaryBlue,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildInfoRow('الاسم الكامل', _employee!.name, Icons.person),
            _buildInfoRow('البريد الإلكتروني', _employee!.email, Icons.email),
            _buildInfoRow('الدور', _getRoleText(_employee!.role), Icons.admin_panel_settings),
          ],
        ),
      ),
    );
  }







  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: () => Navigator.pushNamed(
              context,
              AppRoutes.editEmployee,
              arguments: {'employee': _employee},
            ),
            icon: const Icon(Icons.edit),
            label: const Text('تعديل'),
          ),
        ),
        const SizedBox(width: AppConstants.defaultPadding),

        const SizedBox(width: AppConstants.defaultPadding),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _showDeleteDialog,
            icon: const Icon(Icons.delete),
            label: const Text('حذف'),
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
          ),
        ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(icon, size: 20, color: AppColors.primaryBlue),
          const SizedBox(width: AppConstants.defaultPadding),
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getRoleText(String role) {
    switch (role) {
      case 'admin':
        return 'مدير';
      case 'supervisor':
        return 'مشرف';
      case 'employee':
      default:
        return 'موظف';
    }
  }

  void _showDeleteDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف الموظف "${_employee!.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteEmployee();
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _deleteEmployee() async {
    try {
      await context.read<EmployeesProvider>().deleteEmployee(_employee!.id);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم حذف الموظف بنجاح')),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في حذف الموظف: $e')),
        );
      }
    }
  }
}
