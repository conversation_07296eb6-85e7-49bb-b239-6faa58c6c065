<?php

// Debug Excel generation specifically
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "🔧 Excel Generation Debug Test\n";
echo "=============================\n\n";

try {
    // Change to the Laravel directory
    chdir('C:\wamp64\www\clockin');
    
    // Include Laravel bootstrap
    require_once 'vendor/autoload.php';
    
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "✅ Laravel bootstrapped\n";
    
    // Get user and prepare minimal data
    $user = \App\Models\User::find(5);
    $startDate = \Carbon\Carbon::parse('2025-07-28')->startOfDay();
    $endDate = \Carbon\Carbon::parse('2025-08-04')->endOfDay();
    
    // Get pointages
    $pointages = \App\Models\Pointage::with('site')
        ->where('user_id', 5)
        ->whereBetween('debut_pointage', [$startDate, $endDate])
        ->get();
    
    echo "✅ Found " . $pointages->count() . " pointages\n";
    
    // Create minimal test data
    $testData = [
        'user' => $user,
        'pointages' => $pointages,
        'period' => [
            'start' => $startDate,
            'end' => $endDate
        ],
        'statistics' => [
            'total_pointages' => $pointages->count(),
            'completed_pointages' => $pointages->where('fin_pointage', '!=', null)->count(),
            'active_pointages' => $pointages->where('fin_pointage', null)->count(),
            'total_hours' => 10.5,
            'average_daily_hours' => 1.5,
            'sites_worked' => ['Test Site'],
            'daily_breakdown' => [
                '2025-08-02' => [
                    'date' => '2025-08-02',
                    'pointages' => 3,
                    'hours' => 8.0,
                    'sites' => ['Test Site']
                ]
            ]
        ]
    ];
    
    echo "✅ Test data prepared\n";
    
    // Test creating the export class
    echo "🔄 Creating DetailedPointagesExport...\n";
    $export = new \App\Exports\DetailedPointagesExport($testData);
    echo "✅ Export class created\n";
    
    // Test each sheet individually
    echo "🔄 Testing individual sheets...\n";
    
    $sheets = $export->sheets();
    echo "✅ Got " . count($sheets) . " sheets\n";
    
    foreach ($sheets as $title => $sheet) {
        echo "  📄 Testing sheet: $title\n";
        
        try {
            if (method_exists($sheet, 'array')) {
                $data = $sheet->array();
                echo "    ✅ Array data: " . count($data) . " rows\n";
            }
            
            if (method_exists($sheet, 'headings')) {
                $headings = $sheet->headings();
                echo "    ✅ Headings: " . count($headings) . " columns\n";
            }
        } catch (Exception $e) {
            echo "    ❌ Sheet error: " . $e->getMessage() . "\n";
            throw $e;
        }
    }
    
    // Test Excel generation with error handling
    echo "🔄 Testing Excel generation...\n";
    
    $filename = 'debug_test_' . time() . '.xlsx';
    
    try {
        // Use a more direct approach
        $writer = \Maatwebsite\Excel\Facades\Excel::raw($export, \Maatwebsite\Excel\Excel::XLSX);
        echo "✅ Excel raw generation successful (" . strlen($writer) . " bytes)\n";
        
        // Try to store it
        \Maatwebsite\Excel\Facades\Excel::store($export, $filename, 'local');
        
        $filePath = storage_path('app/' . $filename);
        if (file_exists($filePath)) {
            $fileSize = filesize($filePath);
            echo "✅ File stored successfully: $fileSize bytes\n";
            
            // Clean up
            unlink($filePath);
            echo "🧹 Test file cleaned up\n";
        } else {
            echo "❌ File was not stored\n";
        }
        
    } catch (Exception $e) {
        echo "❌ Excel generation failed: " . $e->getMessage() . "\n";
        echo "📍 File: " . $e->getFile() . " Line: " . $e->getLine() . "\n";
        throw $e;
    }
    
} catch (Exception $e) {
    echo "❌ Fatal error: " . $e->getMessage() . "\n";
    echo "📍 File: " . $e->getFile() . " Line: " . $e->getLine() . "\n";
    echo "📋 Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n🏁 Debug test completed!\n";
?>
