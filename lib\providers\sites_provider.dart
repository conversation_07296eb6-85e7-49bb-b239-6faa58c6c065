import 'dart:math';
import 'package:flutter/foundation.dart';
import '../models/models.dart';
import '../services/services.dart';
import '../constants/app_constants.dart';

enum SitesState {
  initial,
  loading,
  loaded,
  error,
  creating,
  updating,
  deleting,
}

class SitesProvider extends ChangeNotifier {
  final ApiService _apiService = ApiService();
  final StorageService _storageService = StorageService();

  SitesState _state = SitesState.initial;
  List<Site> _sites = [];
  Site? _selectedSite;
  String? _errorMessage;
  bool _isLoading = false;
  int _currentPage = 1;
  bool _hasMoreData = true;
  String _searchQuery = '';

  // Getters
  SitesState get state => _state;
  List<Site> get sites => _sites;
  Site? get selectedSite => _selectedSite;
  String? get errorMessage => _errorMessage;
  bool get isLoading => _isLoading;
  bool get hasMoreData => _hasMoreData;
  int get currentPage => _currentPage;
  String get searchQuery => _searchQuery;
  int get sitesCount => _sites.length;
  bool get hasSites => _sites.isNotEmpty;

  // Initialize
  Future<void> initialize() async {
    _setState(SitesState.loading);
    
    try {
      await _loadCachedSites();
      await refreshSites();
    } catch (e) {
      debugPrint('Sites Provider: Initialization error: $e');
      _setError('خطأ في تهيئة بيانات المواقع');
    }
  }

  // Load cached sites
  Future<void> _loadCachedSites() async {
    try {
      final cachedSites = _storageService.getSites();
      if (cachedSites.isNotEmpty) {
        _sites = cachedSites;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Sites Provider: Error loading cached sites: $e');
    }
  }

  // Refresh sites
  Future<void> refreshSites() async {
    _setLoading(true);
    _clearError();

    try {
      _currentPage = 1;
      _hasMoreData = true;
      _sites.clear();

      await _loadSites();
      _setState(SitesState.loaded);
    } catch (e) {
      debugPrint('Sites Provider: Error refreshing sites: $e');
      _setError('فشل في تحديث المواقع');
    } finally {
      _setLoading(false);
    }
  }

  // Load sites
  Future<void> _loadSites() async {
    try {
      debugPrint('SitesProvider: Loading sites - page: $_currentPage, search: ${_searchQuery.isNotEmpty ? _searchQuery : "none"}');

      final response = await _apiService.getSites(
        page: _currentPage,
        perPage: AppConstants.defaultPageSize,
        search: _searchQuery.isNotEmpty ? _searchQuery : null,
      );

      debugPrint('SitesProvider: API response received - data count: ${response.data.length}, total: ${response.total}, currentPage: ${response.currentPage}');

      if (_currentPage == 1) {
        _sites = response.data;
      } else {
        _sites.addAll(response.data);
      }

      _hasMoreData = response.hasNextPage;

      // Cache sites
      await _storageService.saveSites(_sites);

      debugPrint('SitesProvider: Sites loaded successfully - total sites in memory: ${_sites.length}');
    } catch (e) {
      debugPrint('SitesProvider: Error loading sites: $e');
      rethrow;
    }
  }

  // Load more sites
  Future<void> loadMoreSites() async {
    if (!_hasMoreData || _isLoading) return;

    _setLoading(true);
    
    try {
      _currentPage++;
      await _loadSites();
    } catch (e) {
      debugPrint('Sites Provider: Error loading more sites: $e');
      _currentPage--; // Revert page increment
      _setError('فشل في تحميل المزيد من المواقع');
    } finally {
      _setLoading(false);
    }
  }

  // Search sites
  Future<void> searchSites(String query) async {
    _searchQuery = query;
    _setLoading(true);
    _clearError();

    try {
      _currentPage = 1;
      _hasMoreData = true;
      _sites.clear();

      await _loadSites();
      debugPrint('Sites Provider: Sites searched with query: $query');
    } catch (e) {
      debugPrint('Sites Provider: Error searching sites: $e');
      _setError('فشل في البحث عن المواقع');
    } finally {
      _setLoading(false);
    }
  }

  // Clear search
  Future<void> clearSearch() async {
    if (_searchQuery.isEmpty) return;
    
    _searchQuery = '';
    await refreshSites();
  }

  // Get site by ID
  Future<Site?> getSite(int id) async {
    try {
      // First check if site exists in local list
      final localSites = _sites.where((site) => site.id == id);
      final localSite = localSites.isNotEmpty ? localSites.first : null;

      if (localSite != null) {
        _selectedSite = localSite;
        notifyListeners();
        return localSite;
      }
    } catch (e) {
      debugPrint('Sites Provider: Error checking local sites: $e');
    }

    // Site not found locally, fetch from API
    try {
      final site = await _apiService.getSite(id);
      _selectedSite = site;

      // Add to local list if not already present
      if (!_sites.any((s) => s.id == id)) {
        _sites.add(site);
        await _storageService.saveSites(_sites);
      }

      notifyListeners();
      return site;
    } catch (e) {
      debugPrint('Sites Provider: Error getting site: $e');
      _setError('فشل في تحميل بيانات الموقع');
      return null;
    }
  }

  // Create site
  Future<bool> createSite({
    required String name,
    required double latitude,
    required double longitude,
  }) async {
    _setState(SitesState.creating);
    _clearError();

    try {
      final request = SiteCreateRequest(
        name: name,
        latitude: latitude,
        longitude: longitude,
      );

      final newSite = await _apiService.createSite(request);
      
      // Add to local list
      _sites.insert(0, newSite);
      
      // Update cache
      await _storageService.saveSites(_sites);
      
      _setState(SitesState.loaded);
      debugPrint('Sites Provider: Site created successfully');
      return true;
    } catch (e) {
      debugPrint('Sites Provider: Error creating site: $e');
      if (e is ApiException) {
        _setError(e.message);
      } else {
        _setError('فشل في إنشاء الموقع');
      }
      return false;
    }
  }

  // Update site
  Future<bool> updateSite({
    required int id,
    String? name,
    double? latitude,
    double? longitude,
  }) async {
    _setState(SitesState.updating);
    _clearError();

    try {
      final request = SiteUpdateRequest(
        name: name,
        latitude: latitude,
        longitude: longitude,
      );

      final updatedSite = await _apiService.updateSite(id, request);
      
      // Update in local list
      final index = _sites.indexWhere((site) => site.id == id);
      if (index != -1) {
        _sites[index] = updatedSite;
      }
      
      // Update selected site if it's the same
      if (_selectedSite?.id == id) {
        _selectedSite = updatedSite;
      }
      
      // Update cache
      await _storageService.saveSites(_sites);
      
      _setState(SitesState.loaded);
      debugPrint('Sites Provider: Site updated successfully');
      return true;
    } catch (e) {
      debugPrint('Sites Provider: Error updating site: $e');
      if (e is ApiException) {
        _setError(e.message);
      } else {
        _setError('فشل في تحديث الموقع');
      }
      return false;
    }
  }

  // Delete site
  Future<bool> deleteSite(int id) async {
    _setState(SitesState.deleting);
    _clearError();

    try {
      await _apiService.deleteSite(id);
      
      // Remove from local list
      _sites.removeWhere((site) => site.id == id);
      
      // Clear selected site if it's the same
      if (_selectedSite?.id == id) {
        _selectedSite = null;
      }
      
      // Update cache
      await _storageService.saveSites(_sites);
      
      _setState(SitesState.loaded);
      debugPrint('Sites Provider: Site deleted successfully');
      return true;
    } catch (e) {
      debugPrint('Sites Provider: Error deleting site: $e');
      if (e is ApiException) {
        _setError(e.message);
      } else {
        _setError('فشل في حذف الموقع');
      }
      return false;
    }
  }

  // Assign site to user
  Future<bool> assignSiteToUser(int userId, int siteId) async {
    _setLoading(true);
    _clearError();

    try {
      await _apiService.assignSiteToUser(userId, siteId);
      debugPrint('Sites Provider: Site assigned to user successfully');
      return true;
    } catch (e) {
      debugPrint('Sites Provider: Error assigning site to user: $e');
      if (e is ApiException) {
        _setError(e.message);
      } else {
        _setError('فشل في تخصيص الموقع للموظف');
      }
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Assign site to multiple users
  Future<bool> assignSiteToUsers(int siteId, List<int> userIds) async {
    _setLoading(true);
    _clearError();

    try {
      await _apiService.assignSiteToUsers(siteId, userIds);

      // Update the site in local list with new assignments
      final siteIndex = _sites.indexWhere((site) => site.id == siteId);
      if (siteIndex != -1) {
        // Refresh the specific site to get updated user assignments
        final updatedSite = await _apiService.getSite(siteId);
        _sites[siteIndex] = updatedSite;

        // Update selected site if it's the same
        if (_selectedSite?.id == siteId) {
          _selectedSite = updatedSite;
        }

        // Update cache
        await _storageService.saveSites(_sites);
      }

      debugPrint('Sites Provider: Site assigned to users successfully');
      return true;
    } catch (e) {
      debugPrint('Sites Provider: Error assigning site to users: $e');
      if (e is ApiException) {
        _setError(e.message);
      } else {
        _setError('فشل في تخصيص الموقع للموظفين');
      }
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Get sites for dropdown/selection
  List<Site> getSitesForSelection() {
    return _sites.toList();
  }

  // Find site by name
  Site? findSiteByName(String name) {
    if (name.trim().isEmpty) return null;

    try {
      final matchingSites = _sites.where(
        (site) => site.name.toLowerCase().trim() == name.toLowerCase().trim(),
      );
      return matchingSites.isNotEmpty ? matchingSites.first : null;
    } catch (e) {
      debugPrint('Sites Provider: Error finding site by name: $e');
      return null;
    }
  }

  // Get sites within radius of a location
  List<Site> getSitesNearLocation(
    double latitude,
    double longitude,
    double radiusInMeters,
  ) {
    return _sites.where((site) {
      final distance = _calculateDistance(
        latitude,
        longitude,
        site.latitude,
        site.longitude,
      );
      return distance <= radiusInMeters;
    }).toList();
  }

  // Calculate distance between two points
  double _calculateDistance(double lat1, double lon1, double lat2, double lon2) {
    // Validate coordinates
    if (lat1.abs() > 90 || lat2.abs() > 90 || lon1.abs() > 180 || lon2.abs() > 180) {
      debugPrint('Sites Provider: Invalid coordinates provided');
      return double.infinity;
    }

    // Using Haversine formula
    const double earthRadius = 6371000; // Earth radius in meters
    final double dLat = _toRadians(lat2 - lat1);
    final double dLon = _toRadians(lon2 - lon1);
    final double lat1Rad = _toRadians(lat1);
    final double lat2Rad = _toRadians(lat2);

    final double a = sin(dLat / 2) * sin(dLat / 2) +
        cos(lat1Rad) * cos(lat2Rad) * sin(dLon / 2) * sin(dLon / 2);
    final double c = 2 * asin(sqrt(a.clamp(0.0, 1.0))); // Clamp to avoid NaN

    return earthRadius * c;
  }

  double _toRadians(double degrees) {
    return degrees * (pi / 180);
  }

  // Set selected site
  void setSelectedSite(Site? site) {
    _selectedSite = site;
    notifyListeners();
  }

  // Clear selected site
  void clearSelectedSite() {
    _selectedSite = null;
    notifyListeners();
  }

  // Validate site data
  Map<String, String?> validateSiteData({
    required String name,
    required double latitude,
    required double longitude,
    int? excludeId,
  }) {
    final errors = <String, String?>{};

    // Validate name
    if (name.trim().isEmpty) {
      errors['name'] = 'اسم الموقع مطلوب';
    } else if (name.trim().length < 2) {
      errors['name'] = 'اسم الموقع يجب أن يكون أكثر من حرفين';
    } else if (name.trim().length > 100) {
      errors['name'] = 'اسم الموقع طويل جداً';
    } else {
      // Check if name already exists
      final existingSite = findSiteByName(name);
      if (existingSite != null && existingSite.id != excludeId) {
        errors['name'] = 'اسم الموقع مستخدم بالفعل';
      }
    }

    // Validate latitude
    if (latitude.abs() > 90) {
      errors['latitude'] = 'خط العرض يجب أن يكون بين -90 و 90';
    }

    // Validate longitude
    if (longitude.abs() > 180) {
      errors['longitude'] = 'خط الطول يجب أن يكون بين -180 و 180';
    }

    // Check if coordinates are not zero (unless intentional)
    if (latitude == 0 && longitude == 0) {
      errors['coordinates'] = 'يرجى تحديد إحداثيات صحيحة للموقع';
    }

    return errors;
  }

  // Get site statistics
  Map<String, dynamic> getSiteStatistics() {
    final sitesWithCoordinates = _sites.where((s) => s.latitude != 0 && s.longitude != 0).toList();

    return {
      'total_sites': _sites.length,
      'sites_with_coordinates': sitesWithCoordinates.length,
      'average_latitude': sitesWithCoordinates.isNotEmpty
          ? sitesWithCoordinates.map((s) => s.latitude).reduce((a, b) => a + b) / sitesWithCoordinates.length
          : 0.0,
      'average_longitude': sitesWithCoordinates.isNotEmpty
          ? sitesWithCoordinates.map((s) => s.longitude).reduce((a, b) => a + b) / sitesWithCoordinates.length
          : 0.0,
    };
  }

  // Private helper methods
  void _setState(SitesState newState) {
    _state = newState;
    notifyListeners();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    _state = SitesState.error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    if (_state == SitesState.error) {
      _state = SitesState.loaded;
      notifyListeners();
    }
  }

  // Load sites data
  Future<void> loadSitesData() async {
    _setLoading(true);
    _clearError();

    try {
      _currentPage = 1;
      _hasMoreData = true;
      _sites.clear();

      await _loadSites();
      _setState(SitesState.loaded);
      debugPrint('SitesProvider: Sites data loaded successfully');
    } catch (e) {
      debugPrint('SitesProvider: Error loading sites data: $e');
      _setError('فشل في تحميل بيانات المواقع');
    } finally {
      _setLoading(false);
    }
  }

  // Check location for sites
  Future<void> checkLocationForSites(double latitude, double longitude) async {
    try {
      for (final site in _sites) {
        if (site.latitude != 0 && site.longitude != 0) {
          final distance = _calculateDistance(
            latitude,
            longitude,
            site.latitude,
            site.longitude,
          );

          // Check if within allowed radius
          if (distance <= AppConstants.maxAllowedDistance) {
            debugPrint('Sites Provider: User is within range of site ${site.name}');
            // You can add additional logic here for when user is in range
          }
        }
      }
    } catch (e) {
      debugPrint('Sites Provider: Error checking location for sites: $e');
    }
  }

  // Get nearest site
  Site? getNearestSite(double latitude, double longitude) {
    if (_sites.isEmpty) return null;

    Site? nearestSite;
    double minDistance = double.infinity;

    for (final site in _sites) {
      if (site.latitude != 0 && site.longitude != 0) {
        final distance = _calculateDistance(
          latitude,
          longitude,
          site.latitude,
          site.longitude,
        );

        if (distance < minDistance) {
          minDistance = distance;
          nearestSite = site;
        }
      }
    }

    return nearestSite;
  }

  // Check if location is within any site range
  bool isLocationWithinAnySite(double latitude, double longitude) {
    for (final site in _sites) {
      if (site.latitude != 0 && site.longitude != 0) {
        final distance = _calculateDistance(
          latitude,
          longitude,
          site.latitude,
          site.longitude,
        );

        if (distance <= AppConstants.maxAllowedDistance) {
          return true;
        }
      }
    }
    return false;
  }

  // Get sites within radius
  List<Site> getSitesWithinRadius(double latitude, double longitude, double radius) {
    return _sites.where((site) {
      if (site.latitude == 0 && site.longitude == 0) return false;

      final distance = _calculateDistance(
        latitude,
        longitude,
        site.latitude,
        site.longitude,
      );

      return distance <= radius;
    }).toList();
  }

  // Set test sites (for debugging)
  void setTestSites(List<Site> testSites) {
    debugPrint('SitesProvider: Setting ${testSites.length} test sites');
    _sites = testSites;
    _setState(SitesState.loaded);
    notifyListeners();
  }
}
