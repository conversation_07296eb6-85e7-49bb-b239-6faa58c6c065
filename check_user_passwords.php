<?php

// Script pour vérifier et réinitialiser les mots de passe
require_once 'C:/wamp64/www/clockin/vendor/autoload.php';
$app = require_once 'C:/wamp64/www/clockin/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

echo "=== VÉRIFICATION ET RÉINITIALISATION DES MOTS DE PASSE ===\n\n";

try {
    // 1. Lister tous les utilisateurs
    echo "1. UTILISATEURS ACTUELS:\n";
    echo str_repeat("-", 50) . "\n";
    
    $users = DB::table('users')->select('id', 'name', 'email', 'role')->get();
    foreach ($users as $user) {
        echo sprintf(
            "ID: %d | Name: %s | Email: %s | Role: %s\n",
            $user->id,
            $user->name,
            $user->email,
            $user->role
        );
    }
    
    // 2. Réinitialiser le mot de passe de l'utilisateur ID 5
    echo "\n2. RÉINITIALISATION DU MOT DE PASSE:\n";
    echo str_repeat("-", 50) . "\n";
    
    $userId = 5;
    $newPassword = 'password123';
    $hashedPassword = Hash::make($newPassword);
    
    $updated = DB::table('users')
        ->where('id', $userId)
        ->update(['password' => $hashedPassword]);
    
    if ($updated) {
        echo "✅ Mot de passe mis à jour pour l'utilisateur ID $userId\n";
        echo "Nouveau mot de passe: $newPassword\n";
        
        $user = DB::table('users')->where('id', $userId)->first();
        echo "Email: {$user->email}\n";
        echo "Nom: {$user->name}\n";
        
        // 3. Tester la connexion
        echo "\n3. TEST DE CONNEXION:\n";
        echo str_repeat("-", 50) . "\n";
        
        $loginUrl = 'http://192.168.0.50:8000/api/auth/login';
        $loginData = [
            'email' => $user->email,
            'password' => $newPassword
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $loginUrl);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($loginData));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Accept: application/json'
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        $loginResponse = curl_exec($ch);
        $loginHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($loginHttpCode === 200) {
            $loginResponseData = json_decode($loginResponse, true);
            
            if (isset($loginResponseData['data']['token'])) {
                $token = $loginResponseData['data']['token'];
                echo "✅ Connexion réussie!\n";
                echo "Token: " . substr($token, 0, 30) . "...\n";
                
                // 4. Tester l'endpoint user-assigned
                echo "\n4. TEST ENDPOINT USER-ASSIGNED:\n";
                echo str_repeat("-", 50) . "\n";
                
                $assignedUrl = 'http://192.168.0.50:8000/api/sites/user-assigned';
                
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $assignedUrl);
                curl_setopt($ch, CURLOPT_HTTPHEADER, [
                    'Authorization: Bearer ' . $token,
                    'Accept: application/json'
                ]);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_TIMEOUT, 30);
                
                $assignedResponse = curl_exec($ch);
                $assignedHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);
                
                echo "Code de réponse: $assignedHttpCode\n";
                
                $assignedData = json_decode($assignedResponse, true);
                if ($assignedData) {
                    echo "Réponse:\n";
                    echo json_encode($assignedData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
                    
                    if ($assignedHttpCode === 200 && isset($assignedData['success']) && $assignedData['success']) {
                        echo "\n🎉 SUCCÈS COMPLET!\n";
                        echo "✅ Connexion fonctionne\n";
                        echo "✅ Endpoint user-assigned fonctionne\n";
                        echo "✅ Site assigné trouvé: {$assignedData['data']['name']}\n";
                        echo "✅ Coordonnées: {$assignedData['data']['latitude']}, {$assignedData['data']['longitude']}\n";
                        
                        echo "\n📱 INFORMATIONS POUR L'APPLICATION FLUTTER:\n";
                        echo "Email: {$user->email}\n";
                        echo "Mot de passe: $newPassword\n";
                        echo "Site assigné: {$assignedData['data']['name']}\n";
                        echo "ID du site: {$assignedData['data']['id']}\n";
                        
                    } else {
                        echo "\n❌ Problème avec l'endpoint user-assigned\n";
                    }
                } else {
                    echo "Réponse brute: $assignedResponse\n";
                }
                
            } else {
                echo "❌ Token non trouvé dans la réponse\n";
            }
        } else {
            echo "❌ Échec de la connexion (Code: $loginHttpCode)\n";
            echo "Réponse: $loginResponse\n";
        }
        
    } else {
        echo "❌ Échec de la mise à jour du mot de passe\n";
    }
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "VÉRIFICATION TERMINÉE\n";
echo str_repeat("=", 60) . "\n";
