<?php

namespace App\Services;

use App\Models\User;
use App\Models\Pointage;
use App\Models\Site;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

/**
 * Service de gestion des exports pour le système ClockIn
 * Version optimisée pour Excel avec fallback CSV
 */
class ExportService
{
    private SimpleExportService $simpleExportService;

    public function __construct()
    {
        $this->simpleExportService = new SimpleExportService();
    }

    /**
     * Génère un rapport pour tous les employés
     * Utilise CSV par défaut pour éviter les problèmes de mémoire
     */
    public function generateEmployeeReport(Carbon $startDate, Carbon $endDate, array $options = []): string
    {
        Log::info('ExportService: Generating employee report', [
            'start_date' => $startDate->toDateString(),
            'end_date' => $endDate->toDateString()
        ]);

        // Pour les rapports globaux, utiliser CSV pour éviter les problèmes de mémoire
        return $this->simpleExportService->generateEmployeeReport($startDate, $endDate, $options);
    }

    /**
     * Génère un rapport pour un employé spécifique
     * Essaie Excel pour les rapports individuels (plus petits)
     */
    public function generateIndividualEmployeeReport(int $userId, Carbon $startDate, Carbon $endDate): string
    {
        Log::info('ExportService: Generating individual employee report', [
            'user_id' => $userId,
            'start_date' => $startDate->toDateString(),
            'end_date' => $endDate->toDateString()
        ]);

        // Vérifier la taille des données d'abord
        $pointagesCount = Pointage::where('user_id', $userId)
            ->whereBetween('debut_pointage', [$startDate, $endDate])
            ->count();

        Log::info('ExportService: Pointages count for user', [
            'user_id' => $userId,
            'pointages_count' => $pointagesCount
        ]);

        // Si trop de données, utiliser CSV
        if ($pointagesCount > 100) {
            Log::info('ExportService: Too many pointages, using CSV fallback');
            return $this->simpleExportService->generateIndividualEmployeeReport($userId, $startDate, $endDate);
        }

        // Essayer Excel pour les petits rapports
        try {
            return $this->generateExcelIndividualReport($userId, $startDate, $endDate);
        } catch (\Exception $e) {
            Log::warning('ExportService: Excel generation failed, falling back to CSV', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return $this->simpleExportService->generateIndividualEmployeeReport($userId, $startDate, $endDate);
        }
    }

    /**
     * Génère un rapport par site
     * Utilise CSV par défaut pour éviter les problèmes de mémoire
     */
    public function generateSiteReport(int $siteId, Carbon $startDate, Carbon $endDate): string
    {
        Log::info('ExportService: Generating site report', [
            'site_id' => $siteId,
            'start_date' => $startDate->toDateString(),
            'end_date' => $endDate->toDateString()
        ]);

        // Pour les rapports de site, utiliser CSV pour éviter les problèmes de mémoire
        return $this->simpleExportService->generateSiteReport($siteId, $startDate, $endDate);
    }

    /**
     * Génère un rapport Excel pour un employé individuel (optimisé)
     */
    private function generateExcelIndividualReport(int $userId, Carbon $startDate, Carbon $endDate): string
    {
        // Paramètres de mémoire conservateurs
        ini_set('memory_limit', '128M');
        ini_set('max_execution_time', 120);

        Log::info('ExportService: Attempting Excel generation for individual report');

        $user = User::findOrFail($userId);
        
        // Récupérer seulement les données nécessaires
        $pointages = Pointage::with(['site:id,name'])
            ->where('user_id', $userId)
            ->whereBetween('debut_pointage', [$startDate, $endDate])
            ->orderBy('debut_pointage', 'desc')
            ->limit(50) // Limiter à 50 pointages max pour Excel
            ->get();

        // Préparer des données simplifiées
        $data = [
            'user' => $user,
            'period' => [
                'start' => $startDate,
                'end' => $endDate
            ],
            'pointages' => $pointages,
            'statistics' => [
                'total_pointages' => $pointages->count(),
                'completed_pointages' => $pointages->whereNotNull('fin_pointage')->count(),
                'total_hours' => $pointages->sum(function($p) {
                    return $p->fin_pointage ? 
                        Carbon::parse($p->debut_pointage)->diffInHours(Carbon::parse($p->fin_pointage)) : 0;
                })
            ]
        ];

        $filename = 'rapport_' . str_replace(' ', '_', $user->name) . '_' . $startDate->format('Y-m-d') . '_' . $endDate->format('Y-m-d') . '.xlsx';

        // Utiliser une classe d'export simplifiée
        $export = new \App\Exports\SimpleDetailedPointagesExport($data);
        \Maatwebsite\Excel\Facades\Excel::store($export, $filename, 'local');

        $filePath = storage_path('app/' . $filename);
        
        if (file_exists($filePath)) {
            Log::info('ExportService: Excel individual report generated successfully');
            return $filePath;
        } else {
            throw new \Exception('Excel file was not created');
        }
    }
}
