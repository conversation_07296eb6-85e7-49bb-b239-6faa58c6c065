<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Carbon\Carbon;

class SimpleDetailedPointagesExport implements FromArray, WithHeadings, WithStyles
{
    private $data;

    public function __construct($data)
    {
        $this->data = $data;
    }

    public function array(): array
    {
        $rows = [];
        
        // En-tête du rapport
        $rows[] = ['RAPPORT INDIVIDUEL - ' . $this->data['user']->name];
        $rows[] = ['Période: ' . $this->data['period']['start']->format('d/m/Y') . ' - ' . $this->data['period']['end']->format('d/m/Y')];
        $rows[] = ['Généré le: ' . now()->format('d/m/Y H:i:s')];
        $rows[] = []; // Ligne vide
        
        // Statistiques
        $rows[] = ['STATISTIQUES'];
        $rows[] = ['Total pointages', $this->data['statistics']['total_pointages']];
        $rows[] = ['Pointages terminés', $this->data['statistics']['completed_pointages']];
        $rows[] = ['Total heures', round($this->data['statistics']['total_hours'], 2)];
        $rows[] = []; // Ligne vide
        
        // En-têtes des colonnes
        $rows[] = ['ID', 'Site', 'Date', 'Heure Début', 'Heure Fin', 'Durée (heures)', 'Statut'];
        
        // Données des pointages (limitées pour éviter les problèmes de mémoire)
        $pointages = collect($this->data['pointages'])->take(50); // Limiter à 50 pointages
        
        foreach ($pointages as $pointage) {
            $duration = '';
            if ($pointage->fin_pointage) {
                $start = Carbon::parse($pointage->debut_pointage);
                $end = Carbon::parse($pointage->fin_pointage);
                $duration = round($start->diffInHours($end), 2);
            }
            
            $rows[] = [
                $pointage->id,
                $pointage->site->name ?? 'N/A',
                Carbon::parse($pointage->debut_pointage)->format('d/m/Y'),
                Carbon::parse($pointage->debut_pointage)->format('H:i:s'),
                $pointage->fin_pointage ? Carbon::parse($pointage->fin_pointage)->format('H:i:s') : 'En cours',
                $duration,
                $pointage->fin_pointage ? 'Terminé' : 'En cours'
            ];
        }
        
        return $rows;
    }

    public function headings(): array
    {
        return []; // Pas d'en-têtes séparés car ils sont dans le array()
    }

    public function styles(Worksheet $sheet)
    {
        return [
            // Style pour l'en-tête du rapport
            1 => ['font' => ['bold' => true, 'size' => 14]],
            
            // Style pour les statistiques
            5 => ['font' => ['bold' => true]],
            
            // Style pour les en-têtes de colonnes
            10 => ['font' => ['bold' => true], 'fill' => ['fillType' => 'solid', 'color' => ['rgb' => 'E3F2FD']]],
        ];
    }
}
