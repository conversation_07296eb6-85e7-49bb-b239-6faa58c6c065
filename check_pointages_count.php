<?php

// Check pointages count for user
chdir('C:\wamp64\www\clockin');
require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "📊 Checking pointages count\n";
echo "==========================\n\n";

$userId = 5;
$startDate = '2025-07-28';
$endDate = '2025-08-04';

$count = \App\Models\Pointage::where('user_id', $userId)
    ->whereBetween('debut_pointage', [$startDate, $endDate])
    ->count();

echo "👤 User ID: $userId\n";
echo "📅 Date range: $startDate to $endDate\n";
echo "📊 Pointages count: $count\n";

if ($count > 100) {
    echo "⚠️ Too many pointages for Excel (>100), CSV fallback will be used\n";
} else {
    echo "✅ Pointages count is acceptable for Excel generation\n";
}

echo "\n🏁 Check completed!\n";
?>
