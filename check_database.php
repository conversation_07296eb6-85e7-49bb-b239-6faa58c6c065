<?php

// Script pour vérifier la base de données ClockIn
require_once 'C:/wamp64/www/clockin/vendor/autoload.php';
$app = require_once 'C:/wamp64/www/clockin/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;

echo "=== VÉRIFICATION BASE DE DONNÉES CLOCKIN ===\n\n";

try {
    // 1. Vérifier les utilisateurs
    echo "1. UTILISATEURS:\n";
    echo str_repeat("-", 50) . "\n";
    
    $users = DB::table('users')
        ->select('id', 'name', 'email', 'role', 'default_site_id')
        ->get();
    
    foreach ($users as $user) {
        echo sprintf(
            "ID: %d | Name: %s | Email: %s | Role: %s | DefaultSiteId: %s\n",
            $user->id,
            $user->name,
            $user->email,
            $user->role,
            $user->default_site_id ?? 'NULL'
        );
    }
    
    // 2. Vérifier les sites
    echo "\n2. SITES:\n";
    echo str_repeat("-", 50) . "\n";
    
    $sites = DB::table('sites')->get();
    
    foreach ($sites as $site) {
        echo sprintf(
            "ID: %d | Name: %s | Lat: %s | Lon: %s\n",
            $site->id,
            $site->name,
            $site->latitude,
            $site->longitude
        );
    }
    
    // 3. Vérifier les assignations (table assignments si elle existe)
    echo "\n3. ASSIGNATIONS:\n";
    echo str_repeat("-", 50) . "\n";
    
    try {
        $assignments = DB::table('assignments')
            ->join('users', 'assignments.user_id', '=', 'users.id')
            ->join('sites', 'assignments.site_id', '=', 'sites.id')
            ->select(
                'assignments.id',
                'assignments.user_id',
                'assignments.site_id',
                'users.name as user_name',
                'sites.name as site_name'
            )
            ->get();
        
        if ($assignments->count() > 0) {
            foreach ($assignments as $assignment) {
                echo sprintf(
                    "ID: %d | User: %s (ID: %d) → Site: %s (ID: %d)\n",
                    $assignment->id,
                    $assignment->user_name,
                    $assignment->user_id,
                    $assignment->site_name,
                    $assignment->site_id
                );
            }
        } else {
            echo "Aucune assignation trouvée dans la table 'assignments'\n";
        }
    } catch (Exception $e) {
        echo "Table 'assignments' n'existe pas ou erreur: " . $e->getMessage() . "\n";
    }
    
    // 4. Identifier le problème de l'utilisateur ID 5
    echo "\n4. PROBLÈME UTILISATEUR ID 5:\n";
    echo str_repeat("-", 50) . "\n";
    
    $user5 = DB::table('users')->where('id', 5)->first();
    
    if ($user5) {
        echo sprintf(
            "Utilisateur ID 5: %s (%s)\n",
            $user5->name,
            $user5->email
        );
        echo sprintf(
            "Role: %s\n",
            $user5->role
        );
        echo sprintf(
            "Default Site ID: %s\n",
            $user5->default_site_id ?? 'NULL ❌'
        );
        
        if ($user5->default_site_id === null) {
            echo "\n🔥 PROBLÈME IDENTIFIÉ: L'utilisateur n'a pas de default_site_id!\n";
            echo "\n💡 SOLUTION: Assigner un site à cet utilisateur\n";
            
            if ($sites->count() > 0) {
                $firstSite = $sites->first();
                echo "\nCommande pour corriger:\n";
                echo "UPDATE users SET default_site_id = {$firstSite->id} WHERE id = 5;\n";
                
                // Appliquer la correction automatiquement
                echo "\n🔧 Application de la correction...\n";
                DB::table('users')
                    ->where('id', 5)
                    ->update(['default_site_id' => $firstSite->id]);
                
                echo "✅ Correction appliquée! L'utilisateur ID 5 est maintenant assigné au site '{$firstSite->name}'\n";
            }
        } else {
            $assignedSite = DB::table('sites')->where('id', $user5->default_site_id)->first();
            if ($assignedSite) {
                echo "✅ Utilisateur correctement assigné au site: {$assignedSite->name}\n";
            } else {
                echo "❌ Site assigné (ID: {$user5->default_site_id}) n'existe pas!\n";
            }
        }
    } else {
        echo "❌ Utilisateur ID 5 non trouvé!\n";
    }
    
    // 5. Vérifier les pointages récents
    echo "\n5. POINTAGES RÉCENTS:\n";
    echo str_repeat("-", 50) . "\n";
    
    try {
        $pointages = DB::table('pointages')
            ->join('users', 'pointages.user_id', '=', 'users.id')
            ->leftJoin('sites', 'pointages.site_id', '=', 'sites.id')
            ->select(
                'pointages.id',
                'pointages.user_id',
                'pointages.site_id',
                'pointages.debut_pointage',
                'pointages.fin_pointage',
                'users.name as user_name',
                'sites.name as site_name'
            )
            ->orderBy('pointages.created_at', 'desc')
            ->limit(5)
            ->get();
        
        if ($pointages->count() > 0) {
            foreach ($pointages as $pointage) {
                echo sprintf(
                    "ID: %d | User: %s | Site: %s | Début: %s | Fin: %s\n",
                    $pointage->id,
                    $pointage->user_name,
                    $pointage->site_name ?? 'NULL',
                    $pointage->debut_pointage,
                    $pointage->fin_pointage ?? 'En cours'
                );
            }
        } else {
            echo "Aucun pointage trouvé\n";
        }
    } catch (Exception $e) {
        echo "Erreur lors de la récupération des pointages: " . $e->getMessage() . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "VÉRIFICATION TERMINÉE\n";
echo str_repeat("=", 60) . "\n";
