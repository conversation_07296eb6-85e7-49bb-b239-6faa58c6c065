<?php

// Test the fixed API endpoints
echo "🔧 Testing Fixed Report API\n";
echo "==========================\n\n";

// First, let's get a valid token by logging in
echo "🔐 Getting authentication token...\n";

$loginData = [
    'email' => '<EMAIL>',
    'password' => 'password' // Common default password
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://192.168.200.18:8000/api/auth/login');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($loginData));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json'
]);

$loginResponse = curl_exec($ch);
$loginHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "📊 Login Response Code: $loginHttpCode\n";

if ($loginHttpCode === 200) {
    $loginData = json_decode($loginResponse, true);
    
    if (isset($loginData['data']['token'])) {
        $token = $loginData['data']['token'];
        echo "✅ Authentication successful\n";
        echo "🔑 Token: " . substr($token, 0, 20) . "...\n\n";
        
        // Now test the individual report endpoint
        echo "📊 Testing individual report generation...\n";
        
        $reportData = [
            'start_date' => '2025-07-28',
            'end_date' => '2025-08-04'
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'http://192.168.200.18:8000/api/reports/employees/5');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($reportData));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Accept: application/json',
            'Authorization: Bearer ' . $token
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60); // 60 seconds timeout
        
        echo "📡 Making request to individual report endpoint...\n";
        echo "📦 Data: " . json_encode($reportData) . "\n";
        
        $reportResponse = curl_exec($ch);
        $reportHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);
        
        echo "📊 Report Response Code: $reportHttpCode\n";
        
        if ($curlError) {
            echo "❌ cURL Error: $curlError\n";
        }
        
        echo "📄 Response Body:\n";
        
        if ($reportResponse) {
            $responseData = json_decode($reportResponse, true);
            if ($responseData) {
                echo json_encode($responseData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
                
                if ($reportHttpCode === 200 && isset($responseData['success']) && $responseData['success']) {
                    echo "✅ Individual report generated successfully!\n";
                    echo "📁 Filename: " . $responseData['data']['filename'] . "\n";
                    echo "📏 File size: " . $responseData['data']['file_size'] . "\n";
                    echo "🔗 Download URL: " . $responseData['data']['download_url'] . "\n";
                } else {
                    echo "❌ Report generation failed\n";
                }
            } else {
                echo $reportResponse . "\n";
            }
        } else {
            echo "No response received\n";
        }
        
        // Test employee report (all employees)
        echo "\n📊 Testing employee report generation (all employees)...\n";
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'http://192.168.200.18:8000/api/reports/employees');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($reportData));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Accept: application/json',
            'Authorization: Bearer ' . $token
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60);
        
        $allEmployeesResponse = curl_exec($ch);
        $allEmployeesHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        echo "📊 All Employees Response Code: $allEmployeesHttpCode\n";
        
        if ($allEmployeesResponse) {
            $allEmployeesData = json_decode($allEmployeesResponse, true);
            if ($allEmployeesData) {
                echo json_encode($allEmployeesData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
                
                if ($allEmployeesHttpCode === 200 && isset($allEmployeesData['success']) && $allEmployeesData['success']) {
                    echo "✅ All employees report generated successfully!\n";
                } else {
                    echo "❌ All employees report generation failed\n";
                }
            }
        }
        
    } else {
        echo "❌ Login failed - no token received\n";
        echo "Response: $loginResponse\n";
    }
} else {
    echo "❌ Login failed with code: $loginHttpCode\n";
    echo "Response: $loginResponse\n";
}

echo "\n🏁 API test completed!\n";
?>
