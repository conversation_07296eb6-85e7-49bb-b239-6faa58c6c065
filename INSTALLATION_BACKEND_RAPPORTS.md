# Installation et Configuration du Backend pour les Rapports

## 📋 Vue d'ensemble

Ce guide explique comment installer et configurer le backend PHP Laravel pour que les rapports fonctionnent correctement avec l'application Flutter ClockIn.

## 🔧 Étapes d'installation

### 1. Copier les fichiers du contrôleur

Copiez le contenu du fichier `backend_reports_controller.php` dans votre projet Laravel :

```bash
# Créez le dossier si nécessaire
mkdir -p app/Http/Controllers/Report

# Copiez le contrôleur
cp backend_reports_controller.php app/Http/Controllers/Report/ReportController.php
```

### 2. Installer le trait ApiResponseTrait

Copiez le contenu du fichier `backend_api_response_trait.php` :

```bash
# Créez le dossier si nécessaire
mkdir -p app/Http/Traits

# Copiez le trait
cp backend_api_response_trait.php app/Http/Traits/ApiResponseTrait.php
```

### 3. Ajouter les routes API

Ajoutez les routes suivantes dans votre fichier `routes/api.php` :

```php
// Routes pour les rapports (nécessitent une authentification)
Route::middleware('auth:sanctum')->prefix('reports')->group(function () {
    
    // Endpoint pour récupérer les données de présence
    Route::get('/attendance', [App\Http\Controllers\Report\ReportController::class, 'getAttendanceData']);
    
    // Génération de rapports
    Route::post('/employees', [App\Http\Controllers\Report\ReportController::class, 'generateEmployeeReport']);
    Route::post('/employees/{user_id}', [App\Http\Controllers\Report\ReportController::class, 'generateIndividualReport']);
    Route::post('/sites/{site_id}', [App\Http\Controllers\Report\ReportController::class, 'generateSiteReport']);
    
    // Téléchargement de rapports
    Route::get('/download/{filename}', [App\Http\Controllers\Report\ReportController::class, 'downloadReport']);
});
```

### 4. Créer le dossier de stockage des rapports

```bash
# Créez le dossier pour stocker les fichiers de rapports
mkdir -p storage/app/reports

# Donnez les permissions appropriées
chmod 755 storage/app/reports
```

### 5. Vérifier les dépendances

Assurez-vous que les modèles suivants existent dans votre projet :

- `App\Models\Pointage`
- `App\Models\User`
- `App\Models\Site`

### 6. Vérifier le service ExportService

Le contrôleur utilise `App\Services\ExportService`. Assurez-vous qu'il existe avec les méthodes suivantes :

```php
class ExportService
{
    public function generateEmployeeReport(Carbon $startDate, Carbon $endDate, array $options = []);
    public function generateIndividualEmployeeReport(int $userId, Carbon $startDate, Carbon $endDate);
    public function generateSiteReport(int $siteId, Carbon $startDate, Carbon $endDate);
}
```

## 🌐 Configuration du serveur web

### Pour WAMP (Windows)

1. Placez votre projet Laravel dans `C:\wamp64\www\clockin`
2. Configurez un Virtual Host pour pointer vers `C:\wamp64\www\clockin\public`
3. L'URL de base sera : `http://localhost/clockin/public`

### Configuration Apache Virtual Host

Ajoutez ceci dans votre fichier `httpd-vhosts.conf` :

```apache
<VirtualHost *:80>
    DocumentRoot "C:/wamp64/www/clockin/public"
    ServerName clockin.local
    <Directory "C:/wamp64/www/clockin/public">
        AllowOverride All
        Require all granted
    </Directory>
</VirtualHost>
```

Puis ajoutez dans votre fichier `hosts` :
```
127.0.0.1 clockin.local
```

## 📱 Configuration Frontend

L'URL de base dans le frontend a été mise à jour pour pointer vers :
```dart
static const String baseUrl = 'http://localhost/clockin/public';
```

## 🧪 Test de l'intégration

### 1. Test manuel avec curl

```bash
# Test de l'endpoint d'authentification
curl -X POST http://localhost/clockin/public/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}'

# Test de l'endpoint des données de présence
curl -X GET "http://localhost/clockin/public/api/reports/attendance?start_date=2025-01-01&end_date=2025-01-31&type=all_employees" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 2. Test avec l'application Flutter

Utilisez le fichier `test_reports_integration.dart` fourni pour tester l'intégration complète.

## 🔍 Débogage

### Vérifier les logs Laravel

```bash
# Surveillez les logs en temps réel
tail -f storage/logs/laravel.log
```

### Endpoints disponibles

- `GET /api/reports/attendance` - Récupérer les données de présence
- `POST /api/reports/employees` - Générer un rapport pour tous les employés
- `POST /api/reports/employees/{user_id}` - Générer un rapport individuel
- `POST /api/reports/sites/{site_id}` - Générer un rapport par site
- `GET /api/reports/download/{filename}` - Télécharger un rapport

### Paramètres requis

#### Pour `/api/reports/attendance` :
- `start_date` (required): Format YYYY-MM-DD
- `end_date` (required): Format YYYY-MM-DD
- `type` (required): 'all_employees', 'individual', ou 'site'
- `employee_id` (optional): ID de l'employé pour type='individual'
- `site_id` (optional): ID du site pour type='site'

#### Pour la génération de rapports :
- `start_date` (required): Format YYYY-MM-DD
- `end_date` (required): Format YYYY-MM-DD
- `include_stats` (optional): Boolean pour inclure les statistiques

## ⚠️ Problèmes courants

1. **Erreur 404** : Vérifiez que les routes sont bien ajoutées dans `routes/api.php`
2. **Erreur 500** : Vérifiez les logs Laravel et assurez-vous que tous les modèles existent
3. **Erreur d'authentification** : Vérifiez que Sanctum est configuré correctement
4. **Fichiers non trouvés** : Vérifiez que le dossier `storage/app/reports` existe et a les bonnes permissions

## 🎯 Prochaines étapes

1. Testez chaque endpoint individuellement
2. Vérifiez que les données de présence sont correctement récupérées
3. Testez la génération et le téléchargement des rapports
4. Intégrez avec l'application Flutter

## 📞 Support

Si vous rencontrez des problèmes, vérifiez :
1. Les logs Laravel (`storage/logs/laravel.log`)
2. Les logs du serveur web (Apache/Nginx)
3. La configuration de la base de données
4. Les permissions des fichiers
