import 'dart:convert';

/// Test de conversion des données réelles du backend
/// Ce script simule la conversion des données que nous avons vues dans les logs
void main() {
  print('🧪 Test de Conversion des Données Backend');
  print('==========================================\n');

  // Données réelles du backend (extraites des logs)
  final String backendResponse = '''
{
  "success": true,
  "data": [
    {
      "id": 46,
      "user": {
        "id": 5,
        "name": "سماح زهراوي",
        "email": "<EMAIL>",
        "role": null,
        "created_at": null,
        "updated_at": null
      },
      "site": {
        "id": 5,
        "name": "ain soltan",
        "latitude": null,
        "longitude": null,
        "created_at": null,
        "updated_at": null
      },
      "debut_pointage": "2025-08-03 00:13:29",
      "fin_pointage": "2025-08-03 00:13:54",
      "duree": "00:00:25",
      "debut_latitude": "36.13326110",
      "debut_longitude": "4.73511720",
      "fin_latitude": "36.13325990",
      "fin_longitude": "4.73511840",
      "is_active": false,
      "created_at": "2025-08-03 00:13:29",
      "updated_at": "2025-08-03 00:13:54"
    },
    {
      "id": 45,
      "user": {
        "id": 5,
        "name": "سماح زهراوي",
        "email": "<EMAIL>",
        "role": null,
        "created_at": null,
        "updated_at": null
      },
      "site": {
        "id": 5,
        "name": "ain soltan",
        "latitude": null,
        "longitude": null,
        "created_at": null,
        "updated_at": null
      },
      "debut_pointage": "2025-08-03 00:12:26",
      "fin_pointage": "2025-08-03 00:12:43",
      "duree": "00:00:17",
      "debut_latitude": "36.13326140",
      "debut_longitude": "4.73511480",
      "fin_latitude": "36.13326140",
      "fin_longitude": "4.73511470",
      "is_active": false,
      "created_at": "2025-08-03 00:12:26",
      "updated_at": "2025-08-03 00:12:43"
    }
  ]
}
  ''';

  try {
    // Parser la réponse JSON
    final Map<String, dynamic> response = jsonDecode(backendResponse);
    print('✅ JSON parsé avec succès');
    
    // Extraire les données
    final List<dynamic> rawData = response['data'] ?? [];
    print('📊 Nombre d\'enregistrements: ${rawData.length}');
    
    // Convertir chaque enregistrement
    for (int i = 0; i < rawData.length; i++) {
      final item = rawData[i];
      print('\n🔄 Conversion de l\'enregistrement ${i + 1}:');
      print('   ID: ${item['id']}');
      print('   Utilisateur: ${item['user']?['name']}');
      print('   Site: ${item['site']?['name']}');
      print('   Début: ${item['debut_pointage']}');
      print('   Fin: ${item['fin_pointage']}');
      print('   Durée: ${item['duree']}');
      
      // Simuler la conversion
      final convertedData = convertPointageData(item);
      print('   ✅ Converti:');
      print('      - Nom: ${convertedData['name']}');
      print('      - Site: ${convertedData['site']}');
      print('      - Date: ${convertedData['date']}');
      print('      - Entrée: ${convertedData['checkIn']}');
      print('      - Sortie: ${convertedData['checkOut']}');
      print('      - Total: ${convertedData['totalHours']}');
      print('      - Statut: ${convertedData['status']}');
    }
    
    print('\n🎉 Test de conversion terminé avec succès !');
    
  } catch (e) {
    print('❌ Erreur lors du test: $e');
  }
}

/// Simulation de la méthode _convertPointageData
Map<String, dynamic> convertPointageData(dynamic item) {
  if (item is Map<String, dynamic>) {
    // Extraire les informations du pointage
    final debutPointage = item['debut_pointage'];
    final finPointage = item['fin_pointage'];
    final duree = item['duree'];
    
    String checkIn = '00:00';
    String checkOut = '00:00';
    String date = DateTime.now().toIso8601String().split('T')[0];
    String totalHours = '0 ساعات';
    
    if (debutPointage != null) {
      try {
        // Gérer le format "2025-08-03 00:13:29"
        final debutDateTime = DateTime.parse(debutPointage.toString().replaceAll(' ', 'T'));
        checkIn = '${debutDateTime.hour.toString().padLeft(2, '0')}:${debutDateTime.minute.toString().padLeft(2, '0')}';
        date = debutDateTime.toIso8601String().split('T')[0];
      } catch (e) {
        print('   ⚠️ Erreur parsing debut_pointage: $e');
      }
    }
    
    if (finPointage != null) {
      try {
        // Gérer le format "2025-08-03 00:13:54"
        final finDateTime = DateTime.parse(finPointage.toString().replaceAll(' ', 'T'));
        checkOut = '${finDateTime.hour.toString().padLeft(2, '0')}:${finDateTime.minute.toString().padLeft(2, '0')}';
      } catch (e) {
        print('   ⚠️ Erreur parsing fin_pointage: $e');
      }
    }
    
    // Utiliser la durée du backend si disponible
    if (duree != null && duree.toString().isNotEmpty) {
      totalHours = formatDuration(duree.toString());
    } else {
      totalHours = calculateTotalHours(checkIn, checkOut);
    }
    
    return {
      'id': item['id'] ?? item['user_id'] ?? 0,
      'name': item['user']?['name'] ?? item['employee_name'] ?? 'غير محدد',
      'site': item['site']?['name'] ?? item['site_name'] ?? 'غير محدد',
      'date': date,
      'checkIn': checkIn,
      'checkOut': checkOut,
      'totalHours': totalHours,
      'status': finPointage != null ? 'completed' : 'active',
    };
  }

  // Fallback pour des données mal formatées
  return {
    'id': 0,
    'name': 'غير محدد',
    'site': 'غير محدد',
    'date': DateTime.now().toIso8601String().split('T')[0],
    'checkIn': '00:00',
    'checkOut': '00:00',
    'totalHours': '0 ساعات',
    'status': 'unknown',
  };
}

/// Simulation de la méthode _formatDuration
String formatDuration(String duration) {
  try {
    // Format attendu: "00:00:25" ou "01:30:45"
    final parts = duration.split(':');
    if (parts.length >= 3) {
      final hours = int.parse(parts[0]);
      final minutes = int.parse(parts[1]);
      final seconds = int.parse(parts[2]);
      
      if (hours > 0) {
        if (minutes > 0) {
          return '$hours ساعات و $minutes دقيقة';
        } else {
          return '$hours ساعات';
        }
      } else if (minutes > 0) {
        return '$minutes دقيقة';
      } else if (seconds > 0) {
        return '$seconds ثانية';
      }
    }
    
    return '0 ساعات';
  } catch (e) {
    print('   ⚠️ Erreur formatting duration: $e');
    return '0 ساعات';
  }
}

/// Simulation de la méthode _calculateTotalHours
String calculateTotalHours(String checkIn, String checkOut) {
  try {
    if (checkIn.isEmpty || checkOut.isEmpty || checkIn == '00:00' || checkOut == '00:00') {
      return '0 ساعات';
    }

    final inParts = checkIn.split(':');
    final outParts = checkOut.split(':');

    final inMinutes = int.parse(inParts[0]) * 60 + int.parse(inParts[1]);
    final outMinutes = int.parse(outParts[0]) * 60 + int.parse(outParts[1]);

    final totalMinutes = outMinutes - inMinutes;

    if (totalMinutes <= 0) {
      return '0 ساعات';
    }

    final hours = totalMinutes ~/ 60;
    final minutes = totalMinutes % 60;

    if (minutes == 0) {
      return '$hours ساعات';
    } else {
      return '$hours ساعات و $minutes دقيقة';
    }
  } catch (e) {
    return '0 ساعات';
  }
}
