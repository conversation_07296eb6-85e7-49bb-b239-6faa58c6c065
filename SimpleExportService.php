<?php

namespace App\Services;

use App\Models\User;
use App\Models\Pointage;
use App\Models\Site;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

/**
 * Simplified Export Service for testing
 */
class SimpleExportService
{
    /**
     * Generate a simple CSV report for individual employee
     */
    public function generateIndividualEmployeeReport(int $userId, Carbon $startDate, Carbon $endDate): string
    {
        try {
            Log::info('SimpleExportService: Starting individual report generation', [
                'user_id' => $userId,
                'start_date' => $startDate->toDateString(),
                'end_date' => $endDate->toDateString()
            ]);

            $user = User::findOrFail($userId);
            
            $pointages = Pointage::with(['site'])
                ->where('user_id', $user->id)
                ->whereBetween('debut_pointage', [$startDate, $endDate])
                ->orderBy('debut_pointage', 'desc')
                ->get();

            Log::info('SimpleExportService: Found pointages', [
                'count' => $pointages->count()
            ]);

            // Create CSV content
            $csvContent = $this->generateCSVContent($user, $pointages, $startDate, $endDate);
            
            // Save to file
            $filename = 'rapport_' . str_replace(' ', '_', $user->name) . '_' . $startDate->format('Y-m-d') . '_' . $endDate->format('Y-m-d') . '.csv';
            $filePath = storage_path('app/' . $filename);
            
            file_put_contents($filePath, $csvContent);
            
            Log::info('SimpleExportService: CSV file created', [
                'filename' => $filename,
                'file_size' => filesize($filePath)
            ]);

            return $filePath;

        } catch (\Exception $e) {
            Log::error('SimpleExportService: Error generating report', [
                'user_id' => $userId,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            throw $e;
        }
    }

    /**
     * Generate CSV content for the report
     */
    private function generateCSVContent(User $user, $pointages, Carbon $startDate, Carbon $endDate): string
    {
        $csv = [];
        
        // Header information
        $csv[] = "RAPPORT INDIVIDUEL - " . $user->name;
        $csv[] = "Période: " . $startDate->format('d/m/Y') . " - " . $endDate->format('d/m/Y');
        $csv[] = "Généré le: " . now()->format('d/m/Y H:i:s');
        $csv[] = "";
        
        // Statistics
        $totalPointages = $pointages->count();
        $completedPointages = $pointages->whereNotNull('fin_pointage')->count();
        $totalHours = 0;
        
        foreach ($pointages as $pointage) {
            if ($pointage->fin_pointage) {
                $start = Carbon::parse($pointage->debut_pointage);
                $end = Carbon::parse($pointage->fin_pointage);
                $totalHours += $start->diffInHours($end);
            }
        }
        
        $csv[] = "STATISTIQUES";
        $csv[] = "Total pointages," . $totalPointages;
        $csv[] = "Pointages terminés," . $completedPointages;
        $csv[] = "Total heures," . round($totalHours, 2);
        $csv[] = "";
        
        // Pointages details header
        $csv[] = "DÉTAIL DES POINTAGES";
        $csv[] = "ID,Site,Date,Heure Début,Heure Fin,Durée (heures),Statut,Latitude Début,Longitude Début,Latitude Fin,Longitude Fin";
        
        // Pointages data
        foreach ($pointages as $pointage) {
            $duration = '';
            $hours = 0;
            
            if ($pointage->fin_pointage) {
                $start = Carbon::parse($pointage->debut_pointage);
                $end = Carbon::parse($pointage->fin_pointage);
                $duration = $start->diff($end)->format('%H:%I:%S');
                $hours = $start->diffInHours($end);
            }
            
            $row = [
                $pointage->id,
                '"' . $pointage->site->name . '"',
                Carbon::parse($pointage->debut_pointage)->format('d/m/Y'),
                Carbon::parse($pointage->debut_pointage)->format('H:i:s'),
                $pointage->fin_pointage ? Carbon::parse($pointage->fin_pointage)->format('H:i:s') : 'En cours',
                $hours > 0 ? round($hours, 2) : '',
                $pointage->fin_pointage ? 'Terminé' : 'En cours',
                round($pointage->debut_latitude, 6),
                round($pointage->debut_longitude, 6),
                $pointage->fin_latitude ? round($pointage->fin_latitude, 6) : '',
                $pointage->fin_longitude ? round($pointage->fin_longitude, 6) : ''
            ];
            
            $csv[] = implode(',', $row);
        }
        
        return implode("\n", $csv);
    }

    /**
     * Generate employee report (all employees)
     */
    public function generateEmployeeReport(Carbon $startDate, Carbon $endDate, array $options = []): string
    {
        try {
            Log::info('SimpleExportService: Starting employee report generation');

            $pointages = Pointage::with(['user', 'site'])
                ->whereBetween('debut_pointage', [$startDate, $endDate])
                ->orderBy('debut_pointage', 'desc')
                ->get();

            $csv = [];
            
            // Header
            $csv[] = "RAPPORT GLOBAL DES EMPLOYÉS";
            $csv[] = "Période: " . $startDate->format('d/m/Y') . " - " . $endDate->format('d/m/Y');
            $csv[] = "Généré le: " . now()->format('d/m/Y H:i:s');
            $csv[] = "";
            
            // Data header
            $csv[] = "ID,Employé,Site,Date,Heure Début,Heure Fin,Durée (heures),Statut";
            
            // Data rows
            foreach ($pointages as $pointage) {
                $hours = 0;
                if ($pointage->fin_pointage) {
                    $start = Carbon::parse($pointage->debut_pointage);
                    $end = Carbon::parse($pointage->fin_pointage);
                    $hours = $start->diffInHours($end);
                }
                
                $row = [
                    $pointage->id,
                    '"' . $pointage->user->name . '"',
                    '"' . $pointage->site->name . '"',
                    Carbon::parse($pointage->debut_pointage)->format('d/m/Y'),
                    Carbon::parse($pointage->debut_pointage)->format('H:i:s'),
                    $pointage->fin_pointage ? Carbon::parse($pointage->fin_pointage)->format('H:i:s') : 'En cours',
                    $hours > 0 ? round($hours, 2) : '',
                    $pointage->fin_pointage ? 'Terminé' : 'En cours'
                ];
                
                $csv[] = implode(',', $row);
            }
            
            $filename = 'rapport_employes_' . $startDate->format('Y-m-d') . '_' . $endDate->format('Y-m-d') . '.csv';
            $filePath = storage_path('app/' . $filename);
            
            file_put_contents($filePath, implode("\n", $csv));
            
            Log::info('SimpleExportService: Employee report created', [
                'filename' => $filename,
                'file_size' => filesize($filePath)
            ]);

            return $filePath;

        } catch (\Exception $e) {
            Log::error('SimpleExportService: Error generating employee report', [
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Generate site report
     */
    public function generateSiteReport(int $siteId, Carbon $startDate, Carbon $endDate): string
    {
        try {
            Log::info('SimpleExportService: Starting site report generation', [
                'site_id' => $siteId
            ]);

            $site = Site::findOrFail($siteId);
            
            $pointages = Pointage::with(['user'])
                ->where('site_id', $siteId)
                ->whereBetween('debut_pointage', [$startDate, $endDate])
                ->orderBy('debut_pointage', 'desc')
                ->get();

            $csv = [];
            
            // Header
            $csv[] = "RAPPORT DE SITE - " . $site->name;
            $csv[] = "Période: " . $startDate->format('d/m/Y') . " - " . $endDate->format('d/m/Y');
            $csv[] = "Généré le: " . now()->format('d/m/Y H:i:s');
            $csv[] = "";
            
            // Data header
            $csv[] = "ID,Employé,Date,Heure Début,Heure Fin,Durée (heures),Statut";
            
            // Data rows
            foreach ($pointages as $pointage) {
                $hours = 0;
                if ($pointage->fin_pointage) {
                    $start = Carbon::parse($pointage->debut_pointage);
                    $end = Carbon::parse($pointage->fin_pointage);
                    $hours = $start->diffInHours($end);
                }
                
                $row = [
                    $pointage->id,
                    '"' . $pointage->user->name . '"',
                    Carbon::parse($pointage->debut_pointage)->format('d/m/Y'),
                    Carbon::parse($pointage->debut_pointage)->format('H:i:s'),
                    $pointage->fin_pointage ? Carbon::parse($pointage->fin_pointage)->format('H:i:s') : 'En cours',
                    $hours > 0 ? round($hours, 2) : '',
                    $pointage->fin_pointage ? 'Terminé' : 'En cours'
                ];
                
                $csv[] = implode(',', $row);
            }
            
            $filename = 'rapport_site_' . str_replace(' ', '_', $site->name) . '_' . $startDate->format('Y-m-d') . '_' . $endDate->format('Y-m-d') . '.csv';
            $filePath = storage_path('app/' . $filename);
            
            file_put_contents($filePath, implode("\n", $csv));
            
            Log::info('SimpleExportService: Site report created', [
                'filename' => $filename,
                'file_size' => filesize($filePath)
            ]);

            return $filePath;

        } catch (\Exception $e) {
            Log::error('SimpleExportService: Error generating site report', [
                'site_id' => $siteId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
}
