import 'package:flutter/foundation.dart';
import '../models/models.dart';
import '../services/services.dart';

enum AuthState {
  initial,
  loading,
  authenticated,
  unauthenticated,
  error,
}

class AuthProvider extends ChangeNotifier {
  final AuthService _authService = AuthService();
  final StorageService _storageService = StorageService();

  AuthState _state = AuthState.initial;
  User? _user;
  String? _token;
  String? _errorMessage;
  bool _isLoading = false;

  // Getters
  AuthState get state => _state;
  User? get user => _user;
  String? get token => _token;
  String? get errorMessage => _errorMessage;
  bool get isLoading => _isLoading;
  bool get isAuthenticated => _state == AuthState.authenticated && _user != null;
  bool get isAdmin => _user?.isAdmin ?? false;
  bool get isEmployee => _user?.isEmployee ?? false;
  String? get userRole => _user?.role;
  int? get userId => _user?.id;
  String? get userName => _user?.name;
  String? get userEmail => _user?.email;

  // Initialize authentication state
  Future<void> initialize() async {
    _setState(AuthState.loading);
    
    try {
      await _authService.initialize();
      
      if (_authService.isAuthenticated && !_authService.isTokenExpired) {
        _user = _authService.currentUser;
        _token = _authService.token;
        _setState(AuthState.authenticated);
        debugPrint('Auth Provider: User already authenticated');
      } else {
        await logout();
      }
    } catch (e) {
      debugPrint('Auth Provider: Initialization error: $e');
      _setError('خطأ في تهيئة التطبيق');
    }
  }

  // Login
  Future<bool> login(String email, String password) async {
    _setLoading(true);
    _clearError();

    try {
      final loginResponse = await _authService.login(email, password);
      
      _user = loginResponse.user;
      _token = loginResponse.token;
      
      // Save to local storage
      await _storageService.saveUser(_user!);
      await _storageService.saveToken(_token!);
      
      _setState(AuthState.authenticated);
      debugPrint('Auth Provider: Login successful for ${_user!.email}');
      return true;
    } catch (e) {
      debugPrint('Auth Provider: Login failed: $e');
      if (e is ApiException) {
        _setError(e.message);
      } else {
        _setError('فشل في تسجيل الدخول');
      }
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Logout
  Future<void> logout() async {
    _setLoading(true);
    
    try {
      await _authService.logout();
      await _storageService.clearUser();
      await _storageService.clearToken();
      
      _user = null;
      _token = null;
      _setState(AuthState.unauthenticated);
      debugPrint('Auth Provider: Logout successful');
    } catch (e) {
      debugPrint('Auth Provider: Logout error: $e');
      // Continue with local logout even if API call fails
      _user = null;
      _token = null;
      _setState(AuthState.unauthenticated);
    } finally {
      _setLoading(false);
    }
  }

  // Refresh user data
  Future<bool> refreshUser() async {
    if (!isAuthenticated) return false;

    try {
      final updatedUser = await _authService.refreshUser();
      _user = updatedUser;
      await _storageService.saveUser(_user!);
      notifyListeners();
      debugPrint('Auth Provider: User data refreshed');
      return true;
    } catch (e) {
      debugPrint('Auth Provider: Failed to refresh user: $e');
      if (e is ApiException && e.isUnauthorized) {
        await logout();
      }
      return false;
    }
  }

  // Change password
  Future<bool> changePassword({
    required String currentPassword,
    required String newPassword,
    required String confirmPassword,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      await _authService.changePassword(
        currentPassword: currentPassword,
        newPassword: newPassword,
        confirmPassword: confirmPassword,
      );
      
      debugPrint('Auth Provider: Password changed successfully');
      return true;
    } catch (e) {
      debugPrint('Auth Provider: Failed to change password: $e');
      if (e is ApiException) {
        _setError(e.message);
      } else {
        _setError('فشل في تغيير كلمة المرور');
      }
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Update profile
  Future<bool> updateProfile({String? name, String? email}) async {
    _setLoading(true);
    _clearError();

    try {
      final updatedUser = await _authService.updateProfile(
        name: name,
        email: email,
      );
      
      _user = updatedUser;
      await _storageService.saveUser(_user!);
      notifyListeners();
      debugPrint('Auth Provider: Profile updated successfully');
      return true;
    } catch (e) {
      debugPrint('Auth Provider: Failed to update profile: $e');
      if (e is ApiException) {
        _setError(e.message);
      } else {
        _setError('فشل في تحديث الملف الشخصي');
      }
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Check token validity
  Future<bool> ensureValidToken() async {
    if (!isAuthenticated) return false;

    try {
      return await _authService.ensureValidToken();
    } catch (e) {
      debugPrint('Auth Provider: Token validation failed: $e');
      await logout();
      return false;
    }
  }

  // Auto-refresh token periodically
  void startTokenRefreshTimer() {
    // Check token validity every 5 minutes
    Stream.periodic(const Duration(minutes: 5)).listen((_) async {
      if (isAuthenticated) {
        await ensureValidToken();
      }
    });
  }

  // Listen to authentication state changes
  Stream<AuthState> get authStateStream async* {
    yield _state;
    
    await for (final isAuth in _authService.authStateChanges) {
      if (isAuth && _state != AuthState.authenticated) {
        _setState(AuthState.authenticated);
      } else if (!isAuth && _state == AuthState.authenticated) {
        await logout();
      }
      yield _state;
    }
  }

  // Clear all user data
  Future<void> clearAllData() async {
    try {
      await _authService.clearAllData();
      await _storageService.clearAllData();
      
      _user = null;
      _token = null;
      _setState(AuthState.unauthenticated);
      debugPrint('Auth Provider: All data cleared');
    } catch (e) {
      debugPrint('Auth Provider: Error clearing data: $e');
    }
  }

  // Private helper methods
  void _setState(AuthState newState) {
    _state = newState;
    notifyListeners();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    _state = AuthState.error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    if (_state == AuthState.error) {
      _state = isAuthenticated ? AuthState.authenticated : AuthState.unauthenticated;
      notifyListeners();
    }
  }

  // Get user display name
  String get displayName {
    if (_user?.name != null && _user!.name.isNotEmpty) {
      return _user!.name;
    }
    return _user?.email ?? 'مستخدم';
  }

  // Get user role display text
  String get roleDisplayText {
    return _user?.displayRole ?? 'غير محدد';
  }

  // Check if user has specific role
  bool hasRole(String role) {
    return _user?.role == role;
  }

  // Get user initials for avatar
  String get userInitials {
    if (_user?.name != null && _user!.name.isNotEmpty) {
      final names = _user!.name.split(' ');
      if (names.length >= 2) {
        return '${names[0][0]}${names[1][0]}'.toUpperCase();
      } else {
        return names[0][0].toUpperCase();
      }
    }
    return _user?.email.isNotEmpty == true ? _user!.email[0].toUpperCase() : 'U';
  }


}
