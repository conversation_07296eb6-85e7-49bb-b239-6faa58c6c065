<?php

// Test the fixed ExportService directly
echo "🔧 Testing Fixed ExportService\n";
echo "=============================\n\n";

try {
    // Change to the Laravel directory
    chdir('C:\wamp64\www\clockin');
    
    // Include Laravel bootstrap
    require_once 'vendor/autoload.php';
    
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "✅ Laravel bootstrapped\n";
    
    // Test the fixed ExportService
    $exportService = new \App\Services\ExportService();
    echo "✅ Fixed ExportService instantiated\n";
    
    // Test individual report generation
    echo "🔄 Testing individual report generation...\n";
    
    $userId = 5;
    $startDate = \Carbon\Carbon::parse('2025-07-28')->startOfDay();
    $endDate = \Carbon\Carbon::parse('2025-08-04')->endOfDay();
    
    echo "📅 Date range: {$startDate->toDateString()} to {$endDate->toDateString()}\n";
    echo "👤 User ID: $userId\n";
    
    $filePath = $exportService->generateIndividualEmployeeReport($userId, $startDate, $endDate);
    
    if (file_exists($filePath)) {
        $fileSize = filesize($filePath);
        $filename = basename($filePath);
        $extension = pathinfo($filePath, PATHINFO_EXTENSION);
        
        echo "✅ Individual report generated successfully!\n";
        echo "   📁 File: $filename\n";
        echo "   📏 Size: $fileSize bytes\n";
        echo "   📄 Type: $extension\n";
        
        // Show file content preview if it's CSV
        if ($extension === 'csv') {
            echo "   📄 Content preview (first 10 lines):\n";
            $content = file_get_contents($filePath);
            $lines = explode("\n", $content);
            for ($i = 0; $i < min(10, count($lines)); $i++) {
                echo "      " . ($i + 1) . ": " . $lines[$i] . "\n";
            }
        }
        
        // Clean up test file
        unlink($filePath);
        echo "   🧹 Test file cleaned up\n";
    } else {
        echo "❌ Individual report generation failed - file not found\n";
    }
    
    echo "\n🔄 Testing employee report generation (all employees)...\n";
    
    $filePath2 = $exportService->generateEmployeeReport($startDate, $endDate);
    
    if (file_exists($filePath2)) {
        $fileSize2 = filesize($filePath2);
        $filename2 = basename($filePath2);
        $extension2 = pathinfo($filePath2, PATHINFO_EXTENSION);
        
        echo "✅ Employee report generated successfully!\n";
        echo "   📁 File: $filename2\n";
        echo "   📏 Size: $fileSize2 bytes\n";
        echo "   📄 Type: $extension2\n";
        
        // Clean up test file
        unlink($filePath2);
        echo "   🧹 Test file cleaned up\n";
    } else {
        echo "❌ Employee report generation failed - file not found\n";
    }
    
    echo "\n🔄 Testing site report generation...\n";
    
    // First, let's find a valid site ID
    $sites = \App\Models\Site::all(['id', 'name']);
    if ($sites->count() > 0) {
        $site = $sites->first();
        echo "🏢 Using site: {$site->name} (ID: {$site->id})\n";
        
        $filePath3 = $exportService->generateSiteReport($site->id, $startDate, $endDate);
        
        if (file_exists($filePath3)) {
            $fileSize3 = filesize($filePath3);
            $filename3 = basename($filePath3);
            $extension3 = pathinfo($filePath3, PATHINFO_EXTENSION);
            
            echo "✅ Site report generated successfully!\n";
            echo "   📁 File: $filename3\n";
            echo "   📏 Size: $fileSize3 bytes\n";
            echo "   📄 Type: $extension3\n";
            
            // Clean up test file
            unlink($filePath3);
            echo "   🧹 Test file cleaned up\n";
        } else {
            echo "❌ Site report generation failed - file not found\n";
        }
    } else {
        echo "⚠️ No sites found in database - skipping site report test\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "📍 File: " . $e->getFile() . " Line: " . $e->getLine() . "\n";
    echo "📋 Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n🏁 ExportService test completed!\n";
?>
