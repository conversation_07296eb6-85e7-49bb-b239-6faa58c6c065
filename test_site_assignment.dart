// Test simple pour vérifier la logique de site assigné
// Ce fichier peut être utilisé pour tester la solution

import 'package:flutter/material.dart';

void main() {
  print('🧪 Test de la logique de site assigné');
  
  // Simulation des données utilisateur
  testUserWithDefaultSiteId();
  testUserWithoutDefaultSiteId();
  testNoSitesAvailable();
}

void testUserWithDefaultSiteId() {
  print('\n✅ Test 1: Utilisateur avec defaultSiteId');
  
  // Simulation d'un utilisateur avec defaultSiteId
  final user = {
    'id': 1,
    'name': '<PERSON>',
    'email': '<EMAIL>',
    'default_site_id': 1,
  };
  
  final sites = [
    {'id': 1, 'name': 'Site Casablanca', 'latitude': 33.5731, 'longitude': -7.5898},
    {'id': 2, 'name': 'Site Rabat', 'latitude': 34.0209, 'longitude': -6.8416},
  ];
  
  // Logique de notre solution
  Map<String, dynamic>? assignedSite;
  
  if (user['default_site_id'] != null) {
    assignedSite = sites.firstWhere(
      (site) => site['id'] == user['default_site_id'],
      orElse: () => {},
    );
  }
  
  if (assignedSite != null && assignedSite.isNotEmpty) {
    print('   ✅ Site assigné trouvé: ${assignedSite['name']} (ID: ${assignedSite['id']})');
  } else {
    print('   ❌ Aucun site assigné trouvé');
  }
}

void testUserWithoutDefaultSiteId() {
  print('\n⚠️ Test 2: Utilisateur sans defaultSiteId');
  
  // Simulation d'un utilisateur sans defaultSiteId
  final user = {
    'id': 2,
    'name': 'Fatima',
    'email': '<EMAIL>',
    'default_site_id': null,
  };
  
  final sites = [
    {'id': 1, 'name': 'Site Casablanca', 'latitude': 33.5731, 'longitude': -7.5898},
    {'id': 2, 'name': 'Site Rabat', 'latitude': 34.0209, 'longitude': -6.8416},
  ];
  
  // Logique de notre solution
  Map<String, dynamic>? assignedSite;
  
  if (user['default_site_id'] != null) {
    assignedSite = sites.firstWhere(
      (site) => site['id'] == user['default_site_id'],
      orElse: () => {},
    );
  }
  
  // Fallback vers premier site disponible
  if ((assignedSite == null || assignedSite.isEmpty) && sites.isNotEmpty) {
    assignedSite = sites.first;
    print('   ⚠️ Utilisation du premier site disponible: ${assignedSite['name']} (ID: ${assignedSite['id']})');
  }
  
  if (assignedSite != null && assignedSite.isNotEmpty) {
    print('   ✅ Site assigné (fallback): ${assignedSite['name']} (ID: ${assignedSite['id']})');
  } else {
    print('   ❌ Aucun site assigné trouvé');
  }
}

void testNoSitesAvailable() {
  print('\n❌ Test 3: Aucun site disponible');
  
  // Simulation d'un utilisateur avec defaultSiteId mais aucun site disponible
  final user = {
    'id': 3,
    'name': 'Omar',
    'email': '<EMAIL>',
    'default_site_id': 1,
  };
  
  final sites = <Map<String, dynamic>>[];  // Aucun site disponible
  
  // Logique de notre solution
  Map<String, dynamic>? assignedSite;
  
  if (user['default_site_id'] != null) {
    try {
      assignedSite = sites.firstWhere(
        (site) => site['id'] == user['default_site_id'],
        orElse: () => {},
      );
    } catch (e) {
      print('   ❌ Site avec ID ${user['default_site_id']} non trouvé');
    }
  }
  
  // Fallback vers premier site disponible
  if ((assignedSite == null || assignedSite.isEmpty) && sites.isNotEmpty) {
    assignedSite = sites.first;
  }
  
  if (assignedSite != null && assignedSite.isNotEmpty) {
    print('   ✅ Site assigné: ${assignedSite['name']} (ID: ${assignedSite['id']})');
  } else {
    print('   ❌ Aucun site assigné trouvé - Afficher message d\'erreur');
    print('   📱 Message à afficher: "لم يتم تخصيص موقع لك. يرجى التواصل مع الإدارة."');
  }
}

// Test de la structure de requête check-location selon Postman
void testLocationCheckRequest() {
  print('\n🔍 Test 4: Structure de requête check-location');
  
  final siteId = 1;
  final latitude = 33.5731;
  final longitude = -7.5898;
  
  final request = {
    'site_id': siteId,
    'latitude': latitude,
    'longitude': longitude,
  };
  
  print('   📤 Requête check-location:');
  print('   {');
  print('     "site_id": ${request['site_id']},');
  print('     "latitude": ${request['latitude']},');
  print('     "longitude": ${request['longitude']}');
  print('   }');
  
  // Simulation de la réponse attendue
  final expectedResponse = {
    'success': true,
    'data': {
      'is_within_radius': true,
      'distance': 25.5,
      'site': {
        'id': siteId,
        'name': 'Site Casablanca',
        'latitude': latitude,
        'longitude': longitude,
      }
    }
  };
  
  print('   📥 Réponse attendue:');
  print('   {');
  print('     "success": ${expectedResponse['success']},');
  print('     "data": {');
  print('       "is_within_radius": ${(expectedResponse['data'] as Map)['is_within_radius']},');
  print('       "distance": ${(expectedResponse['data'] as Map)['distance']},');
  print('       "site": { ... }');
  print('     }');
  print('   }');
}
