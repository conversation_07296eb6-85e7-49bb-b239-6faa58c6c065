<?php

// Test final de l'endpoint user-assigned
echo "=== TEST FINAL DE L'ENDPOINT USER-ASSIGNED ===\n";

// 1. Connexion de l'utilisateur
echo "1. CONNEXION DE L'UTILISATEUR:\n";
echo str_repeat("-", 40) . "\n";

$loginUrl = 'http://192.168.0.50:8000/api/auth/login';
$loginData = [
    'email' => '<EMAIL>',
    'password' => 'password123'
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $loginUrl);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($loginData));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);

$loginResponse = curl_exec($ch);
$loginHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($loginHttpCode === 200) {
    $loginResponseData = json_decode($loginResponse, true);
    
    if (isset($loginResponseData['data']['token'])) {
        $token = $loginResponseData['data']['token'];
        echo "✅ Connexion réussie!\n";
        echo "Utilisateur: {$loginResponseData['data']['user']['name']}\n";
        echo "ID: {$loginResponseData['data']['user']['id']}\n";
        echo "Email: {$loginResponseData['data']['user']['email']}\n";
        echo "Token: " . substr($token, 0, 30) . "...\n";
        
        // 2. Test de l'endpoint user-assigned
        echo "\n2. TEST ENDPOINT USER-ASSIGNED:\n";
        echo str_repeat("-", 40) . "\n";
        
        $assignedUrl = 'http://192.168.0.50:8000/api/sites/user-assigned';
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $assignedUrl);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $token,
            'Accept: application/json'
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        $assignedResponse = curl_exec($ch);
        $assignedHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        echo "URL: $assignedUrl\n";
        echo "Code de réponse: $assignedHttpCode\n";
        
        if ($assignedResponse) {
            $assignedData = json_decode($assignedResponse, true);
            if ($assignedData) {
                echo "Réponse JSON:\n";
                echo json_encode($assignedData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
                
                if ($assignedHttpCode === 200 && isset($assignedData['success']) && $assignedData['success']) {
                    echo "\n🎉 SUCCÈS COMPLET!\n";
                    echo "✅ Backend: Endpoint fonctionne parfaitement\n";
                    echo "✅ Site assigné: {$assignedData['data']['name']}\n";
                    echo "✅ ID du site: {$assignedData['data']['id']}\n";
                    echo "✅ Coordonnées: {$assignedData['data']['latitude']}, {$assignedData['data']['longitude']}\n";
                    
                    echo "\n📱 INSTRUCTIONS POUR FLUTTER:\n";
                    echo str_repeat("-", 40) . "\n";
                    echo "1. L'endpoint /api/sites/user-assigned fonctionne maintenant\n";
                    echo "2. Redémarrer l'application Flutter\n";
                    echo "3. Se connecter avec:\n";
                    echo "   Email: <EMAIL>\n";
                    echo "   Mot de passe: password123\n";
                    echo "4. L'application devrait maintenant afficher:\n";
                    echo "   Site: {$assignedData['data']['name']}\n";
                    echo "   Au lieu de: \"لم يتم تخصيص موقع\"\n";
                    
                    echo "\n🔧 VÉRIFICATION FLUTTER:\n";
                    echo str_repeat("-", 40) . "\n";
                    echo "Le frontend Flutter utilise déjà la bonne méthode:\n";
                    echo "- ApiService.getUserAssignedSite()\n";
                    echo "- Endpoint: /api/sites/user-assigned\n";
                    echo "- Le problème était côté backend (endpoint manquant)\n";
                    echo "- Maintenant résolu!\n";
                    
                    echo "\n📊 RÉSUMÉ DE LA SOLUTION:\n";
                    echo str_repeat("-", 40) . "\n";
                    echo "❌ Problème initial: \"لم يتم تخصيص موقع\" (pas de site assigné)\n";
                    echo "🔍 Cause: L'endpoint /api/sites/user-assigned n'existait pas\n";
                    echo "✅ Solution: Ajout de l'endpoint + méthode SiteController::getUserAssigned\n";
                    echo "✅ Base de données: Utilisateur ID 5 assigné au site 'ain soltan'\n";
                    echo "✅ Backend: Endpoint fonctionnel\n";
                    echo "✅ Frontend: Code déjà correct, juste besoin de redémarrer\n";
                    
                } else {
                    echo "\n❌ PROBLÈME DÉTECTÉ:\n";
                    if (isset($assignedData['message'])) {
                        echo "Message: {$assignedData['message']}\n";
                    }
                    if (isset($assignedData['message_ar'])) {
                        echo "Message AR: {$assignedData['message_ar']}\n";
                    }
                }
            } else {
                echo "Réponse brute: $assignedResponse\n";
            }
        } else {
            echo "❌ Aucune réponse reçue\n";
        }
        
    } else {
        echo "❌ Token non trouvé dans la réponse de connexion\n";
        echo "Réponse: $loginResponse\n";
    }
} else {
    echo "❌ Échec de la connexion (Code: $loginHttpCode)\n";
    echo "Réponse: $loginResponse\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "TEST FINAL TERMINÉ\n";
echo str_repeat("=", 60) . "\n";
