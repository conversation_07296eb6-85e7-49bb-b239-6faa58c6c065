import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../providers/attendance_provider.dart';

class StatusCard extends StatelessWidget {
  final AttendanceProvider attendanceProvider;

  const StatusCard({super.key, required this.attendanceProvider});

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الحالة الحالية',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatusItem(
                    context,
                    icon: Icons.access_time,
                    title: 'الحالة',
                    value: attendanceProvider.getStatusText(),
                    color: attendanceProvider.hasActivePointage
                        ? AppColors.success
                        : AppColors.textSecondary,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatusItem(
                    context,
                    icon: Icons.schedule,
                    title: 'ساعات اليوم',
                    value: attendanceProvider.getWorkingHoursToday(),
                    color: AppColors.primaryBlue,
                  ),
                ),
              ],
            ),
            if (attendanceProvider.assignedSite != null) ...[
              const SizedBox(height: 16),
              _buildStatusItem(
                context,
                icon: Icons.location_on,
                title: 'الموقع المخصص',
                value: attendanceProvider.assignedSite!.name,
                color: AppColors.info,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatusItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String value,
    required Color color,
  }) {
    return Row(
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
              ),
              Text(
                value,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: color,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
