<?php

namespace App\Services;

use App\Models\User;
use App\Models\Pointage;
use App\Models\Site;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\DetailedPointagesExport;
use App\Exports\EmployeeReportExport;
use App\Exports\SiteReportExport;

/**
 * Service de gestion des exports pour le système ClockIn
 * Version avec fallback CSV en cas d'erreur Excel
 */
class ExportService
{
    private SimpleExportService $simpleExportService;

    public function __construct()
    {
        $this->simpleExportService = new SimpleExportService();
    }

    /**
     * Génère un rapport Excel détaillé pour tous les employés
     */
    public function generateEmployeeReport(Carbon $startDate, Carbon $endDate, array $options = []): string
    {
        try {
            Log::info('ExportService: Starting employee report generation (Excel)', [
                'start_date' => $startDate->toDateString(),
                'end_date' => $endDate->toDateString()
            ]);

            $data = $this->prepareEmployeeReportData($startDate, $endDate, $options);

            $filename = 'rapport_employes_' . $startDate->format('Y-m-d') . '_' . $endDate->format('Y-m-d') . '.xlsx';

            // Increase memory and time limits
            ini_set('memory_limit', '512M');
            ini_set('max_execution_time', 300);

            Excel::store(new EmployeeReportExport($data), $filename, 'local');

            $filePath = storage_path('app/' . $filename);
            
            if (file_exists($filePath)) {
                Log::info('ExportService: Excel employee report generated successfully');
                return $filePath;
            } else {
                throw new \Exception('Excel file was not created');
            }

        } catch (\Exception $e) {
            Log::warning('ExportService: Excel generation failed, falling back to CSV', [
                'error' => $e->getMessage()
            ]);

            // Fallback to CSV
            return $this->simpleExportService->generateEmployeeReport($startDate, $endDate, $options);
        }
    }

    /**
     * Génère un rapport Excel pour un employé spécifique
     */
    public function generateIndividualEmployeeReport(int $userId, Carbon $startDate, Carbon $endDate): string
    {
        try {
            Log::info('ExportService: Starting individual report generation (Excel)', [
                'user_id' => $userId,
                'start_date' => $startDate->toDateString(),
                'end_date' => $endDate->toDateString()
            ]);

            $user = User::findOrFail($userId);
            $data = $this->prepareIndividualEmployeeData($user, $startDate, $endDate);

            $filename = 'rapport_' . str_replace(' ', '_', $user->name) . '_' . $startDate->format('Y-m-d') . '_' . $endDate->format('Y-m-d') . '.xlsx';

            // Increase memory and time limits
            ini_set('memory_limit', '512M');
            ini_set('max_execution_time', 300);

            Excel::store(new DetailedPointagesExport($data), $filename, 'local');

            $filePath = storage_path('app/' . $filename);
            
            if (file_exists($filePath)) {
                Log::info('ExportService: Excel individual report generated successfully');
                return $filePath;
            } else {
                throw new \Exception('Excel file was not created');
            }

        } catch (\Exception $e) {
            Log::warning('ExportService: Excel generation failed, falling back to CSV', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            // Fallback to CSV
            return $this->simpleExportService->generateIndividualEmployeeReport($userId, $startDate, $endDate);
        }
    }

    /**
     * Génère un rapport Excel par site
     */
    public function generateSiteReport(int $siteId, Carbon $startDate, Carbon $endDate): string
    {
        try {
            Log::info('ExportService: Starting site report generation (Excel)', [
                'site_id' => $siteId,
                'start_date' => $startDate->toDateString(),
                'end_date' => $endDate->toDateString()
            ]);

            $site = Site::findOrFail($siteId);
            $data = $this->prepareSiteReportData($site, $startDate, $endDate);

            $filename = 'rapport_site_' . str_replace(' ', '_', $site->name) . '_' . $startDate->format('Y-m-d') . '_' . $endDate->format('Y-m-d') . '.xlsx';

            // Increase memory and time limits
            ini_set('memory_limit', '512M');
            ini_set('max_execution_time', 300);

            Excel::store(new SiteReportExport($data), $filename, 'local');

            $filePath = storage_path('app/' . $filename);
            
            if (file_exists($filePath)) {
                Log::info('ExportService: Excel site report generated successfully');
                return $filePath;
            } else {
                throw new \Exception('Excel file was not created');
            }

        } catch (\Exception $e) {
            Log::warning('ExportService: Excel generation failed, falling back to CSV', [
                'site_id' => $siteId,
                'error' => $e->getMessage()
            ]);

            // Fallback to CSV
            return $this->simpleExportService->generateSiteReport($siteId, $startDate, $endDate);
        }
    }

    /**
     * Prépare les données pour le rapport global des employés
     */
    private function prepareEmployeeReportData(Carbon $startDate, Carbon $endDate, array $options): array
    {
        // Récupérer tous les pointages dans la période
        $pointages = Pointage::with(['user', 'site'])
            ->whereBetween('debut_pointage', [$startDate, $endDate])
            ->orderBy('debut_pointage', 'desc')
            ->get();

        // Grouper par employé
        $employeeData = [];

        foreach ($pointages as $pointage) {
            $userId = $pointage->user_id;

            if (!isset($employeeData[$userId])) {
                $employeeData[$userId] = [
                    'user' => $pointage->user,
                    'pointages' => [],
                    'statistics' => [
                        'total_pointages' => 0,
                        'completed_pointages' => 0,
                        'total_hours' => 0,
                        'average_daily_hours' => 0,
                        'sites_worked' => []
                    ]
                ];
            }

            $employeeData[$userId]['pointages'][] = $pointage;
            $employeeData[$userId]['statistics']['total_pointages']++;

            if ($pointage->fin_pointage) {
                $employeeData[$userId]['statistics']['completed_pointages']++;

                // Calculer les heures travaillées
                $hours = Carbon::parse($pointage->debut_pointage)
                    ->diffInHours(Carbon::parse($pointage->fin_pointage));
                $employeeData[$userId]['statistics']['total_hours'] += $hours;
            }

            // Ajouter le site aux sites travaillés
            if (!in_array($pointage->site->name, $employeeData[$userId]['statistics']['sites_worked'])) {
                $employeeData[$userId]['statistics']['sites_worked'][] = $pointage->site->name;
            }
        }

        // Calculer les moyennes
        foreach ($employeeData as &$data) {
            $totalDays = $startDate->diffInDays($endDate) + 1;
            $data['statistics']['average_daily_hours'] = $totalDays > 0
                ? round($data['statistics']['total_hours'] / $totalDays, 2)
                : 0;
        }

        return [
            'period' => [
                'start' => $startDate,
                'end' => $endDate
            ],
            'employees' => $employeeData,
            'summary' => $this->calculateGlobalSummary($employeeData)
        ];
    }

    /**
     * Prépare les données pour un employé individuel
     */
    private function prepareIndividualEmployeeData(User $user, Carbon $startDate, Carbon $endDate): array
    {
        $pointages = Pointage::with(['site'])
            ->where('user_id', $user->id)
            ->whereBetween('debut_pointage', [$startDate, $endDate])
            ->orderBy('debut_pointage', 'desc')
            ->get();

        // Calculer les statistiques détaillées
        $statistics = [
            'total_pointages' => $pointages->count(),
            'completed_pointages' => $pointages->whereNotNull('fin_pointage')->count(),
            'active_pointages' => $pointages->whereNull('fin_pointage')->count(),
            'total_hours' => 0,
            'average_daily_hours' => 0,
            'sites_worked' => [],
            'daily_breakdown' => []
        ];

        // Analyser chaque pointage
        foreach ($pointages as $pointage) {
            $date = Carbon::parse($pointage->debut_pointage)->format('Y-m-d');

            if (!isset($statistics['daily_breakdown'][$date])) {
                $statistics['daily_breakdown'][$date] = [
                    'date' => $date,
                    'pointages' => 0,
                    'hours' => 0,
                    'sites' => []
                ];
            }

            $statistics['daily_breakdown'][$date]['pointages']++;

            if ($pointage->fin_pointage) {
                $hours = Carbon::parse($pointage->debut_pointage)
                    ->diffInHours(Carbon::parse($pointage->fin_pointage));
                $statistics['total_hours'] += $hours;
                $statistics['daily_breakdown'][$date]['hours'] += $hours;
            }

            if (!in_array($pointage->site->name, $statistics['sites_worked'])) {
                $statistics['sites_worked'][] = $pointage->site->name;
            }

            if (!in_array($pointage->site->name, $statistics['daily_breakdown'][$date]['sites'])) {
                $statistics['daily_breakdown'][$date]['sites'][] = $pointage->site->name;
            }
        }

        $totalDays = $startDate->diffInDays($endDate) + 1;
        $statistics['average_daily_hours'] = $totalDays > 0
            ? round($statistics['total_hours'] / $totalDays, 2)
            : 0;

        return [
            'user' => $user,
            'period' => [
                'start' => $startDate,
                'end' => $endDate
            ],
            'pointages' => $pointages,
            'statistics' => $statistics
        ];
    }

    /**
     * Prépare les données pour un rapport de site
     */
    private function prepareSiteReportData(Site $site, Carbon $startDate, Carbon $endDate): array
    {
        $pointages = Pointage::with(['user'])
            ->where('site_id', $site->id)
            ->whereBetween('debut_pointage', [$startDate, $endDate])
            ->orderBy('debut_pointage', 'desc')
            ->get();

        // Grouper par employé
        $employeeData = [];
        $siteStatistics = [
            'total_pointages' => $pointages->count(),
            'completed_pointages' => $pointages->whereNotNull('fin_pointage')->count(),
            'total_hours' => 0,
            'unique_employees' => $pointages->pluck('user_id')->unique()->count(),
            'daily_breakdown' => []
        ];

        foreach ($pointages as $pointage) {
            $userId = $pointage->user_id;
            $date = Carbon::parse($pointage->debut_pointage)->format('Y-m-d');

            // Données par employé
            if (!isset($employeeData[$userId])) {
                $employeeData[$userId] = [
                    'user' => $pointage->user,
                    'pointages' => [],
                    'total_hours' => 0
                ];
            }

            $employeeData[$userId]['pointages'][] = $pointage;

            // Statistiques journalières
            if (!isset($siteStatistics['daily_breakdown'][$date])) {
                $siteStatistics['daily_breakdown'][$date] = [
                    'date' => $date,
                    'pointages' => 0,
                    'hours' => 0,
                    'employees' => []
                ];
            }

            $siteStatistics['daily_breakdown'][$date]['pointages']++;

            if (!in_array($userId, $siteStatistics['daily_breakdown'][$date]['employees'])) {
                $siteStatistics['daily_breakdown'][$date]['employees'][] = $userId;
            }

            if ($pointage->fin_pointage) {
                $hours = Carbon::parse($pointage->debut_pointage)
                    ->diffInHours(Carbon::parse($pointage->fin_pointage));
                $employeeData[$userId]['total_hours'] += $hours;
                $siteStatistics['total_hours'] += $hours;
                $siteStatistics['daily_breakdown'][$date]['hours'] += $hours;
            }
        }

        return [
            'site' => $site,
            'period' => [
                'start' => $startDate,
                'end' => $endDate
            ],
            'pointages' => $pointages,
            'employees' => $employeeData,
            'statistics' => $siteStatistics
        ];
    }

    /**
     * Calcule un résumé global
     */
    private function calculateGlobalSummary(array $employeeData): array
    {
        $totalEmployees = count($employeeData);
        $totalPointages = 0;
        $totalHours = 0;
        $allSites = [];

        foreach ($employeeData as $data) {
            $totalPointages += $data['statistics']['total_pointages'];
            $totalHours += $data['statistics']['total_hours'];
            $allSites = array_merge($allSites, $data['statistics']['sites_worked']);
        }

        return [
            'total_employees' => $totalEmployees,
            'total_pointages' => $totalPointages,
            'total_hours' => round($totalHours, 2),
            'average_hours_per_employee' => $totalEmployees > 0 ? round($totalHours / $totalEmployees, 2) : 0,
            'unique_sites' => count(array_unique($allSites))
        ];
    }
}
