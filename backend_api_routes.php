<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Report\ReportController;

/*
|--------------------------------------------------------------------------
| API Routes pour les Rapports
|--------------------------------------------------------------------------
|
| Routes API pour la génération et le téléchargement de rapports
| Ces routes doivent être ajoutées au fichier routes/api.php du backend Laravel
|
*/

// Routes pour les rapports (nécessitent une authentification)
Route::middleware('auth:sanctum')->prefix('reports')->group(function () {
    
    // Endpoint pour récupérer les données de présence
    Route::get('/attendance', [ReportController::class, 'getAttendanceData']);
    
    // Génération de rapports
    Route::post('/employees', [ReportController::class, 'generateEmployeeReport']);
    Route::post('/employees/{user_id}', [ReportController::class, 'generateIndividualReport']);
    Route::post('/sites/{site_id}', [ReportController::class, 'generateSiteReport']);
    
    // Téléchargement de rapports
    Route::get('/download/{filename}', [ReportController::class, 'downloadReport']);
});

/*
|--------------------------------------------------------------------------
| Instructions d'installation
|--------------------------------------------------------------------------
|
| 1. Copiez le contenu du fichier backend_reports_controller.php dans 
|    app/Http/Controllers/Report/ReportController.php
|
| 2. Ajoutez ces routes dans votre fichier routes/api.php existant
|
| 3. Assurez-vous que le trait ApiResponseTrait existe dans 
|    app/Http/Traits/ApiResponseTrait.php
|
| 4. Vérifiez que le service ExportService existe dans 
|    app/Services/ExportService.php
|
| 5. Assurez-vous que les modèles Pointage, User et Site existent
|
| 6. Créez le dossier storage/app/reports/ pour stocker les fichiers générés
|
*/
