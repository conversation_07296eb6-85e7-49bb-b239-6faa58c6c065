import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';

enum ActionButtonSize { small, medium, large }

enum ActionButtonVariant { primary, secondary, success, warning, error, info }

class ActionButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final IconData? icon;
  final ActionButtonSize size;
  final ActionButtonVariant variant;
  final bool isLoading;
  final bool isExpanded;

  const ActionButton({
    super.key,
    required this.text,
    this.onPressed,
    this.icon,
    this.size = ActionButtonSize.medium,
    this.variant = ActionButtonVariant.primary,
    this.isLoading = false,
    this.isExpanded = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDisabled = onPressed == null || isLoading;

    return SizedBox(
      width: isExpanded ? double.infinity : null,
      height: _getHeight(),
      child: ElevatedButton(
        onPressed: isDisabled ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: _getBackgroundColor(),
          foregroundColor: _getForegroundColor(),
          elevation: variant == ActionButtonVariant.primary ? 2 : 0,
          shadowColor: _getBackgroundColor().withValues(alpha: 0.3),
          padding: EdgeInsets.symmetric(
            horizontal: _getHorizontalPadding(),
            vertical: _getVerticalPadding(),
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(_getBorderRadius()),
            side: _getBorderSide(),
          ),
          textStyle: theme.textTheme.labelLarge?.copyWith(
            fontSize: _getFontSize(),
            fontWeight: FontWeight.w600,
            letterSpacing: 0.1,
          ),
        ),
        child: _buildContent(),
      ),
    );
  }

  Widget _buildContent() {
    if (isLoading) {
      return SizedBox(
        width: _getIconSize(),
        height: _getIconSize(),
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(_getForegroundColor()),
        ),
      );
    }

    if (icon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: _getIconSize()),
          SizedBox(width: _getIconSpacing()),
          Text(text),
        ],
      );
    }

    return Text(text);
  }

  double _getHeight() {
    switch (size) {
      case ActionButtonSize.small:
        return 36;
      case ActionButtonSize.medium:
        return 48;
      case ActionButtonSize.large:
        return 56;
    }
  }

  double _getHorizontalPadding() {
    switch (size) {
      case ActionButtonSize.small:
        return 16;
      case ActionButtonSize.medium:
        return 24;
      case ActionButtonSize.large:
        return 32;
    }
  }

  double _getVerticalPadding() {
    switch (size) {
      case ActionButtonSize.small:
        return 8;
      case ActionButtonSize.medium:
        return 12;
      case ActionButtonSize.large:
        return 16;
    }
  }

  double _getBorderRadius() {
    switch (size) {
      case ActionButtonSize.small:
        return 8;
      case ActionButtonSize.medium:
        return 12;
      case ActionButtonSize.large:
        return 16;
    }
  }

  double _getFontSize() {
    switch (size) {
      case ActionButtonSize.small:
        return 14;
      case ActionButtonSize.medium:
        return 16;
      case ActionButtonSize.large:
        return 18;
    }
  }

  double _getIconSize() {
    switch (size) {
      case ActionButtonSize.small:
        return 16;
      case ActionButtonSize.medium:
        return 20;
      case ActionButtonSize.large:
        return 24;
    }
  }

  double _getIconSpacing() {
    switch (size) {
      case ActionButtonSize.small:
        return 6;
      case ActionButtonSize.medium:
        return 8;
      case ActionButtonSize.large:
        return 10;
    }
  }

  Color _getBackgroundColor() {
    switch (variant) {
      case ActionButtonVariant.primary:
        return AppColors.primaryBlue;
      case ActionButtonVariant.secondary:
        return AppColors.surfaceGrey;
      case ActionButtonVariant.success:
        return AppColors.success;
      case ActionButtonVariant.warning:
        return AppColors.warning;
      case ActionButtonVariant.error:
        return AppColors.error;
      case ActionButtonVariant.info:
        return AppColors.info;
    }
  }

  Color _getForegroundColor() {
    switch (variant) {
      case ActionButtonVariant.primary:
      case ActionButtonVariant.success:
      case ActionButtonVariant.warning:
      case ActionButtonVariant.error:
      case ActionButtonVariant.info:
        return AppColors.textWhite;
      case ActionButtonVariant.secondary:
        return AppColors.textPrimary;
    }
  }

  BorderSide _getBorderSide() {
    if (variant == ActionButtonVariant.secondary) {
      return BorderSide(
        color: AppColors.textHint.withValues(alpha: 0.3),
        width: 1,
      );
    }
    return BorderSide.none;
  }
}

// Convenience widgets for common button types
class PrimaryActionButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final IconData? icon;
  final ActionButtonSize size;
  final bool isLoading;
  final bool isExpanded;

  const PrimaryActionButton({
    super.key,
    required this.text,
    this.onPressed,
    this.icon,
    this.size = ActionButtonSize.medium,
    this.isLoading = false,
    this.isExpanded = true,
  });

  @override
  Widget build(BuildContext context) {
    return ActionButton(
      text: text,
      onPressed: onPressed,
      icon: icon,
      size: size,
      variant: ActionButtonVariant.primary,
      isLoading: isLoading,
      isExpanded: isExpanded,
    );
  }
}

class SecondaryActionButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final IconData? icon;
  final ActionButtonSize size;
  final bool isLoading;
  final bool isExpanded;

  const SecondaryActionButton({
    super.key,
    required this.text,
    this.onPressed,
    this.icon,
    this.size = ActionButtonSize.medium,
    this.isLoading = false,
    this.isExpanded = true,
  });

  @override
  Widget build(BuildContext context) {
    return ActionButton(
      text: text,
      onPressed: onPressed,
      icon: icon,
      size: size,
      variant: ActionButtonVariant.secondary,
      isLoading: isLoading,
      isExpanded: isExpanded,
    );
  }
}
