import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_constants.dart';
import '../../../providers/providers.dart';
import '../../../services/services.dart';
import '../../../models/models.dart';

class DownloadManagementTab extends StatefulWidget {
  const DownloadManagementTab({super.key});

  @override
  State<DownloadManagementTab> createState() => _DownloadManagementTabState();
}

class _DownloadManagementTabState extends State<DownloadManagementTab> {
  final ReportsService _reportsService = ReportsService();
  String _searchQuery = '';
  String _sortBy = 'date'; // date, name, size
  bool _sortAscending = false;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildSearchAndFilterBar(),
        Expanded(
          child: Consumer<ReportsProvider>(
            builder: (context, reportsProvider, child) {
              if (reportsProvider.generatedReports.isEmpty) {
                return _buildEmptyState();
              }

              final filteredReports = _getFilteredAndSortedReports(
                reportsProvider.generatedReports,
              );

              return RefreshIndicator(
                onRefresh: () async {
                  // Refresh reports list if needed
                },
                child: ListView.separated(
                  padding: const EdgeInsets.all(AppConstants.defaultPadding),
                  itemCount: filteredReports.length,
                  separatorBuilder: (context, index) =>
                      const SizedBox(height: 8),
                  itemBuilder: (context, index) {
                    final report = filteredReports[index];
                    return _buildReportCard(report);
                  },
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSearchAndFilterBar() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: const BoxDecoration(
        color: AppColors.backgroundGrey,
        border: Border(bottom: BorderSide(color: AppColors.borderLight)),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextField(
                  decoration: InputDecoration(
                    hintText: 'البحث في التقارير...',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(
                        color: AppColors.borderLight,
                      ),
                    ),
                    filled: true,
                    fillColor: AppColors.backgroundWhite,
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
              ),
              const SizedBox(width: AppConstants.smallPadding),
              PopupMenuButton<String>(
                icon: const Icon(Icons.sort),
                tooltip: 'ترتيب حسب',
                onSelected: (value) {
                  setState(() {
                    if (_sortBy == value) {
                      _sortAscending = !_sortAscending;
                    } else {
                      _sortBy = value;
                      _sortAscending = false;
                    }
                  });
                },
                itemBuilder: (context) => [
                  PopupMenuItem(
                    value: 'date',
                    child: Row(
                      children: [
                        Icon(
                          _sortBy == 'date'
                              ? (_sortAscending
                                    ? Icons.arrow_upward
                                    : Icons.arrow_downward)
                              : Icons.calendar_today,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        const Text('التاريخ'),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'name',
                    child: Row(
                      children: [
                        Icon(
                          _sortBy == 'name'
                              ? (_sortAscending
                                    ? Icons.arrow_upward
                                    : Icons.arrow_downward)
                              : Icons.abc,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        const Text('الاسم'),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'size',
                    child: Row(
                      children: [
                        Icon(
                          _sortBy == 'size'
                              ? (_sortAscending
                                    ? Icons.arrow_upward
                                    : Icons.arrow_downward)
                              : Icons.storage,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        const Text('الحجم'),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Row(
            children: [
              Chip(
                label: Text(
                  'إجمالي التقارير: ${context.watch<ReportsProvider>().reportsCount}',
                ),
                backgroundColor: AppColors.primaryBlueLight.withValues(
                  alpha: 0.1,
                ),
                labelStyle: const TextStyle(color: AppColors.primaryBlue),
              ),
              const SizedBox(width: 8),
              if (_searchQuery.isNotEmpty)
                Chip(
                  label: Text('البحث: $_searchQuery'),
                  backgroundColor: AppColors.warning.withValues(alpha: 0.1),
                  labelStyle: const TextStyle(color: AppColors.warning),
                  deleteIcon: const Icon(Icons.close, size: 18),
                  onDeleted: () {
                    setState(() {
                      _searchQuery = '';
                    });
                  },
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.folder_open,
              size: 80,
              color: AppColors.textSecondary.withValues(alpha: 0.5),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              'لا توجد تقارير متاحة',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: AppColors.textSecondary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              'قم بإنشاء تقرير جديد من تبويب "إنشاء التقارير"',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: AppColors.textSecondary),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            ElevatedButton.icon(
              onPressed: () {
                DefaultTabController.of(context)?.animateTo(0);
              },
              icon: const Icon(Icons.add),
              label: const Text('إنشاء تقرير جديد'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryBlue,
                foregroundColor: AppColors.textWhite,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReportCard(report) {
    final isRecent = _reportsService.isRecentReport(report.generatedAt);
    final reportType = _reportsService.getReportType(report.filename);

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppColors.primaryBlueLight.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.description,
                    color: AppColors.primaryBlue,
                    size: 24,
                  ),
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              report.filename,
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          if (isRecent)
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: AppColors.success.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: const Text(
                                'جديد',
                                style: TextStyle(
                                  color: AppColors.success,
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        reportType,
                        style: const TextStyle(
                          color: AppColors.primaryBlue,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Row(
              children: [
                _buildInfoChip(
                  Icons.storage,
                  'الحجم: ${report.fileSize}',
                  AppColors.info,
                ),
                const SizedBox(width: 8),
                _buildInfoChip(
                  Icons.access_time,
                  _formatDateTime(report.generatedAt),
                  AppColors.textSecondary,
                ),
              ],
            ),
            if (report.period != null) ...[
              const SizedBox(height: AppConstants.smallPadding),
              _buildInfoChip(
                Icons.date_range,
                'من ${report.period!.start} إلى ${report.period!.end}',
                AppColors.warning,
              ),
            ],
            const SizedBox(height: AppConstants.defaultPadding),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _downloadReport(report.filename),
                    icon: const Icon(Icons.download, size: 18),
                    label: const Text('تحميل'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primaryBlue,
                      foregroundColor: AppColors.textWhite,
                      padding: const EdgeInsets.symmetric(vertical: 8),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                OutlinedButton.icon(
                  onPressed: () => _shareReport(report),
                  icon: const Icon(Icons.share, size: 18),
                  label: const Text('مشاركة'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppColors.primaryBlue,
                    side: const BorderSide(color: AppColors.primaryBlue),
                    padding: const EdgeInsets.symmetric(vertical: 8),
                  ),
                ),
                const SizedBox(width: 8),
                IconButton(
                  onPressed: () => _deleteReport(report.filename),
                  icon: const Icon(Icons.delete, color: AppColors.error),
                  tooltip: 'حذف التقرير',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoChip(IconData icon, String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  List<dynamic> _getFilteredAndSortedReports(List<dynamic> reports) {
    var filtered = reports.where((report) {
      if (_searchQuery.isEmpty) return true;
      return report.filename.toLowerCase().contains(_searchQuery.toLowerCase());
    }).toList();

    filtered.sort((a, b) {
      int comparison = 0;

      switch (_sortBy) {
        case 'date':
          comparison = DateTime.parse(
            a.generatedAt,
          ).compareTo(DateTime.parse(b.generatedAt));
          break;
        case 'name':
          comparison = a.filename.compareTo(b.filename);
          break;
        case 'size':
          // Simple size comparison based on file size string
          final aSize = _parseSizeString(a.fileSize);
          final bSize = _parseSizeString(b.fileSize);
          comparison = aSize.compareTo(bSize);
          break;
      }

      return _sortAscending ? comparison : -comparison;
    });

    return filtered;
  }

  double _parseSizeString(String sizeString) {
    final parts = sizeString.split(' ');
    if (parts.length != 2) return 0;

    final value = double.tryParse(parts[0]) ?? 0;
    final unit = parts[1].toUpperCase();

    switch (unit) {
      case 'B':
        return value;
      case 'KB':
        return value * 1024;
      case 'MB':
        return value * 1024 * 1024;
      case 'GB':
        return value * 1024 * 1024 * 1024;
      default:
        return value;
    }
  }

  Future<void> _downloadReport(String filename) async {
    try {
      final reportsProvider = context.read<ReportsProvider>();
      final result = await reportsProvider.downloadReport(filename);
      final success = result?.success ?? false;

      if (mounted) {
        if (success && result != null) {
          if (result.isExcelFile) {
            // Fichier Excel téléchargé et sauvegardé
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('تم تحميل وحفظ ملف Excel بنجاح'),
                backgroundColor: AppColors.success,
                action: SnackBarAction(
                  label: 'فتح',
                  onPressed: () async {
                    if (result.filePath != null) {
                      final opened = await reportsProvider.openExcelFile(
                        result.filePath!,
                      );
                      if (!opened && mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text(
                              'لا يمكن فتح الملف. تأكد من وجود تطبيق Excel',
                            ),
                            backgroundColor: AppColors.warning,
                          ),
                        );
                      }
                    }
                  },
                ),
              ),
            );
          } else {
            // Fichier CSV téléchargé
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('تم تحميل التقرير بنجاح'),
                backgroundColor: AppColors.success,
                action: SnackBarAction(
                  label: 'عرض المحتوى',
                  onPressed: () {
                    _showReportContent(
                      context,
                      result.filename,
                      result.content,
                    );
                  },
                ),
              ),
            );
          }
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('فشل في تحميل التقرير'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل التقرير: ${e.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  Future<void> _shareReport(dynamic report) async {
    // Implement share functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('ميزة المشاركة ستكون متاحة قريباً'),
        backgroundColor: AppColors.info,
      ),
    );
  }

  Future<void> _deleteReport(String filename) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف التقرير "$filename"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: AppColors.textWhite,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      context.read<ReportsProvider>().removeReport(filename);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم حذف التقرير بنجاح'),
          backgroundColor: AppColors.success,
        ),
      );
    }
  }

  String _formatDateTime(String dateTimeString) {
    try {
      final dateTime = DateTime.parse(dateTimeString);
      return DateFormat('dd/MM/yyyy HH:mm').format(dateTime);
    } catch (e) {
      return dateTimeString;
    }
  }

  void _showReportContent(
    BuildContext context,
    String filename,
    String content,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'محتوى التقرير: $filename',
          style: const TextStyle(fontSize: 16),
        ),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: SingleChildScrollView(
            child: SelectableText(
              content,
              style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }
}
