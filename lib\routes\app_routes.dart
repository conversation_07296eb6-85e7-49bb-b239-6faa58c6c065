import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../screens/screens.dart';
import '../models/models.dart';
import '../providers/auth_provider.dart';

class AppRoutes {
  // Route names
  static const String splash = '/';
  static const String login = '/login';
  static const String dashboard = '/dashboard';
  
  // Admin routes
  static const String adminDashboard = '/admin/dashboard';
  static const String employeesManagement = '/admin/employees';
  static const String employeeDetails = '/admin/employees/details';
  static const String addEmployee = '/admin/employees/add';
  static const String editEmployee = '/admin/employees/edit';
  static const String sitesManagement = '/admin/sites';
  static const String siteDetails = '/admin/sites/details';
  static const String addSite = '/admin/sites/add';
  static const String editSite = '/admin/sites/edit';
  static const String siteAssignment = '/admin/sites/assignment';
  static const String reportsScreen = '/admin/reports';
  static const String pointagesManagement = '/admin/pointages';

  static const String monitoringScreen = '/admin/monitoring';
  static const String settingsScreen = '/admin/settings';
  
  // User routes
  static const String userDashboard = '/user/dashboard';
  static const String attendanceScreen = '/user/attendance';
  static const String userProfile = '/user/profile';
  static const String profileScreen = '/user/profile';

  
  // Common routes
  static const String changePassword = '/change-password';
  static const String about = '/about';

  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case splash:
        return MaterialPageRoute(builder: (_) => const SplashScreen());
      
      case login:
        return MaterialPageRoute(builder: (_) => const LoginScreen());
      
      case dashboard:
        return MaterialPageRoute(builder: (_) => const DashboardScreen());
      
      // Admin routes
      case adminDashboard:
        return _createAdminRoute(const AdminDashboardScreen());
      
      case employeesManagement:
        return _createAdminRoute(const EmployeesManagementScreen());
      
      case employeeDetails:
        final args = settings.arguments as Map<String, dynamic>?;
        final employeeId = args?['employeeId'] as int?;
        return _createAdminRoute(EmployeeDetailsScreen(employeeId: employeeId!));
      
      case addEmployee:
        return _createAdminRoute(const AddEmployeeScreen());
      
      case editEmployee:
        final args = settings.arguments as Map<String, dynamic>?;
        final employee = args?['employee'] as User?;
        return _createAdminRoute(EditEmployeeScreen(employee: employee!));
      
      case sitesManagement:
        return _createAdminRoute(const SitesManagementScreen());
      
      case siteDetails:
        final args = settings.arguments as Map<String, dynamic>?;
        final siteId = args?['siteId'] as int?;
        if (siteId == null) {
          return _createAdminRoute(
            const Scaffold(
              body: Center(
                child: Text('خطأ: لم يتم العثور على معرف الموقع'),
              ),
            ),
          );
        }
        return _createAdminRoute(SiteDetailsScreen(siteId: siteId));

      case addSite:
        return _createAdminRoute(const AddSiteScreen());

      case editSite:
        final args = settings.arguments as Map<String, dynamic>?;
        final site = args?['site'];
        if (site == null) {
          return _createAdminRoute(
            const Scaffold(
              body: Center(
                child: Text('خطأ: لم يتم العثور على بيانات الموقع'),
              ),
            ),
          );
        }
        return _createAdminRoute(EditSiteScreen(site: site));

      case siteAssignment:
        final args = settings.arguments as Map<String, dynamic>?;
        final site = args?['site'] as Site?;
        if (site == null) {
          return _createAdminRoute(
            const Scaffold(
              body: Center(
                child: Text('خطأ: لم يتم العثور على بيانات الموقع'),
              ),
            ),
          );
        }
        return _createAdminRoute(SiteAssignmentScreen(site: site));

      case pointagesManagement:
        return _createAdminRoute(const PointagesManagementScreen());

      case reportsScreen:
        return _createAdminRoute(const ReportsScreen());
      
      case monitoringScreen:
        return _createAdminRoute(const EmployeeMonitoringScreen());
      
      case settingsScreen:
        return _createAdminRoute(const SettingsScreen());
      
      // User routes
      case userDashboard:
        return _createUserRoute(const UserDashboardScreen());
      
      case attendanceScreen:
        return _createUserRoute(const PointageScreen());

      case profileScreen:
        return _createUserRoute(const ProfileScreen());



      // Common routes
      case changePassword:
        return MaterialPageRoute(builder: (_) => const ChangePasswordScreen());
      
      case about:
        return MaterialPageRoute(builder: (_) => const AboutScreen());

      default:
        return MaterialPageRoute(
          builder: (_) => const NotFoundScreen(),
        );
    }
  }

  static MaterialPageRoute _createAdminRoute(Widget screen) {
    return MaterialPageRoute(
      builder: (_) => AdminGuard(child: screen),
    );
  }

  static MaterialPageRoute _createUserRoute(Widget screen) {
    return MaterialPageRoute(
      builder: (_) => UserGuard(child: screen),
    );
  }
}

// Route guards
class AdminGuard extends StatelessWidget {
  final Widget child;

  const AdminGuard({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, _) {
        if (!authProvider.isAuthenticated) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            Navigator.pushReplacementNamed(context, AppRoutes.login);
          });
          return const Scaffold(
            body: Center(child: CircularProgressIndicator()),
          );
        }

        if (!authProvider.isAdmin) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            Navigator.pushReplacementNamed(context, AppRoutes.userDashboard);
          });
          return const Scaffold(
            body: Center(child: CircularProgressIndicator()),
          );
        }

        return child;
      },
    );
  }
}

class UserGuard extends StatelessWidget {
  final Widget child;

  const UserGuard({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, _) {
        if (!authProvider.isAuthenticated) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            Navigator.pushReplacementNamed(context, AppRoutes.login);
          });
          return const Scaffold(
            body: Center(child: CircularProgressIndicator()),
          );
        }

        if (authProvider.isAdmin) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            Navigator.pushReplacementNamed(context, AppRoutes.adminDashboard);
          });
          return const Scaffold(
            body: Center(child: CircularProgressIndicator()),
          );
        }

        return child;
      },
    );
  }
}

class NotFoundScreen extends StatelessWidget {
  const NotFoundScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('صفحة غير موجودة')),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 100, color: Colors.grey),
            SizedBox(height: 20),
            Text(
              'الصفحة المطلوبة غير موجودة',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 10),
            Text('تحقق من الرابط وحاول مرة أخرى'),
          ],
        ),
      ),
    );
  }
}
