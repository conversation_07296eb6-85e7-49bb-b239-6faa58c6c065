import 'package:flutter_test/flutter_test.dart';
import 'package:clockin/services/excel_generator_service.dart';
import 'package:clockin/services/reports_service.dart';
import 'dart:convert';

void main() {
  test('Simple Excel Content Test', () async {
    final startDate = DateTime(2025, 8, 1);
    final endDate = DateTime(2025, 8, 4);
    
    final excelData = await ExcelGeneratorService.generateExcelReport(
      reportType: 'all_employees',
      startDate: startDate,
      endDate: endDate,
    );
    
    print('Excel data length: ${excelData.length} bytes');
    
    // Ignorer les 3 premiers bytes (BOM UTF-8) et décoder correctement
    final contentBytes = excelData.skip(3).toList();
    final content = utf8.decode(contentBytes);
    
    print('Content (first 500 chars):');
    print(content.substring(0, content.length > 500 ? 500 : content.length));
    print('\n--- Full Content ---');
    print(content);
    
    // Vérifier que le contenu contient les éléments attendus
    expect(content.contains('تقرير حضور جميع الموظفين'), isTrue);
    expect(content.contains('اسم الموظف'), isTrue);
    expect(content.contains('أحمد محمد علي'), isTrue);
    expect(content.contains('الإحصائيات'), isTrue);

    // Vérifier que le nom de fichier a l'extension .csv
    final filename = ExcelGeneratorService.generateFileName('all_employees');
    expect(filename.endsWith('.csv'), isTrue);
    print('Generated filename: $filename');
  });

  test('Excel Generator with Custom Data', () async {
    final startDate = DateTime(2025, 8, 1);
    final endDate = DateTime(2025, 8, 4);

    // Données personnalisées simulant des données de base de données
    final customData = [
      {
        'id': 1,
        'name': 'محمد أحمد السعيد',
        'site': 'مكتب الرياض الرئيسي',
        'date': '2025-08-01',
        'checkIn': '08:00',
        'checkOut': '17:00',
        'totalHours': '9 ساعات',
      },
      {
        'id': 2,
        'name': 'فاطمة علي محمود',
        'site': 'مكتب جدة',
        'date': '2025-08-01',
        'checkIn': '09:00',
        'checkOut': '18:00',
        'totalHours': '9 ساعات',
      },
    ];

    // Générer le rapport avec les données personnalisées
    final excelData = await ExcelGeneratorService.generateExcelReport(
      reportType: 'all_employees',
      startDate: startDate,
      endDate: endDate,
      employeeData: customData,
    );

    // Décoder le contenu
    final contentBytes = excelData.skip(3).toList();
    final content = utf8.decode(contentBytes);

    // Vérifier que les données personnalisées sont présentes
    expect(content.contains('محمد أحمد السعيد'), isTrue);
    expect(content.contains('فاطمة علي محمود'), isTrue);
    expect(content.contains('مكتب الرياض الرئيسي'), isTrue);
    expect(content.contains('مكتب جدة'), isTrue);

    print('Custom data test passed - CSV contains real database-like data');
  });
}
