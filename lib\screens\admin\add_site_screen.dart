import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../providers/providers.dart';
import '../../widgets/common/custom_text_field.dart';
import '../../widgets/common/custom_button.dart';
import '../../widgets/maps/algeria_location_picker.dart';

class AddSiteScreen extends StatefulWidget {
  const AddSiteScreen({super.key});

  @override
  State<AddSiteScreen> createState() => _AddSiteScreenState();
}

class _AddSiteScreenState extends State<AddSiteScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _addressController = TextEditingController();
  final _radiusController = TextEditingController(text: '100');
  
  LatLng? _selectedLocation;
  String? _selectedAddress;
  bool _isLoading = false;

  @override
  void dispose() {
    _nameController.dispose();
    _addressController.dispose();
    _radiusController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundGrey,
      appBar: AppBar(
        title: const Text('إضافة موقع عمل جديد'),
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: AppColors.textWhite,
        elevation: 0,
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildBasicInfoSection(),
              const SizedBox(height: AppConstants.defaultPadding),
              _buildLocationSection(),
              const SizedBox(height: AppConstants.defaultPadding * 2),
              _buildSaveButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.business,
                  color: AppColors.primaryBlue,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'معلومات الموقع الأساسية',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primaryBlue,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            CustomTextField(
              controller: _nameController,
              labelText: 'اسم الموقع',
              hintText: 'مثال: مكتب الجزائر الرئيسي',
              prefixIcon: Icons.business,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال اسم الموقع';
                }
                if (value.trim().length < 3) {
                  return 'يجب أن يكون اسم الموقع 3 أحرف على الأقل';
                }
                return null;
              },
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            CustomTextField(
              controller: _addressController,
              labelText: 'العنوان',
              hintText: 'سيتم ملؤه تلقائياً عند اختيار الموقع على الخريطة',
              prefixIcon: Icons.location_on,
              maxLines: 2,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال العنوان أو اختيار موقع على الخريطة';
                }
                return null;
              },
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            CustomTextField(
              controller: _radiusController,
              labelText: 'نطاق العمل (بالمتر)',
              hintText: '100',
              prefixIcon: Icons.radio_button_unchecked,
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال نطاق العمل';
                }
                final radius = double.tryParse(value);
                if (radius == null || radius < 50 || radius > 1000) {
                  return 'يجب أن يكون النطاق بين 50 و 1000 متر';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLocationSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: AlgeriaLocationPicker(
          initialLocation: _selectedLocation,
          initialRadius: double.tryParse(_radiusController.text) ?? 100,
          onLocationSelected: (location, address) {
            setState(() {
              _selectedLocation = location;
              _selectedAddress = address;
              if (_addressController.text.isEmpty || _addressController.text == _selectedAddress) {
                _addressController.text = address;
              }
            });
          },
          onRadiusChanged: (radius) {
            setState(() {
              _radiusController.text = radius.toInt().toString();
            });
          },
        ),
      ),
    );
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      child: CustomButton(
        text: 'حفظ الموقع',
        onPressed: _isLoading ? null : _saveSite,
        isLoading: _isLoading,
        type: ButtonType.primary,
      ),
    );
  }

  void _saveSite() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedLocation == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار موقع على الخريطة'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final siteProvider = Provider.of<SitesProvider>(context, listen: false);
      
      await siteProvider.createSite(
        name: _nameController.text.trim(),
        latitude: _selectedLocation!.latitude,
        longitude: _selectedLocation!.longitude,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إنشاء الموقع بنجاح'),
            backgroundColor: AppColors.success,
          ),
        );
        Navigator.pop(context, true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إنشاء الموقع: ${e.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
