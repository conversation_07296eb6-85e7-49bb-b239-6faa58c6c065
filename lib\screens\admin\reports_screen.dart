import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../providers/providers.dart';
import '../../widgets/common/custom_error_widget.dart';
import 'widgets/excel_generation_tab.dart';
import 'widgets/local_files_tab.dart';

class ReportsScreen extends StatefulWidget {
  const ReportsScreen({super.key});

  @override
  State<ReportsScreen> createState() => _ReportsScreenState();
}

class _ReportsScreenState extends State<ReportsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _initializeData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _initializeData() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ReportsProvider>().initialize();
      context.read<EmployeesProvider>().initialize();
      context.read<SitesProvider>().initialize();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.table_chart, color: AppColors.textWhite, size: 24),
            SizedBox(width: 8),
            Text(
              'تقارير الحضور',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
            ),
          ],
        ),
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: AppColors.textWhite,
        elevation: 2,
        centerTitle: true,
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppColors.textWhite,
          unselectedLabelColor: AppColors.textWhite.withValues(alpha: 0.7),
          indicatorColor: AppColors.textWhite,
          indicatorWeight: 3,
          labelStyle: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 14,
          ),
          unselectedLabelStyle: const TextStyle(
            fontWeight: FontWeight.normal,
            fontSize: 14,
          ),
          tabs: const [
            Tab(icon: Icon(Icons.add_chart, size: 20), text: 'إنشاء تقرير'),
            Tab(
              icon: Icon(Icons.folder_open, size: 20),
              text: 'الملفات المحفوظة',
            ),
          ],
        ),
      ),
      body: Consumer<ReportsProvider>(
        builder: (context, reportsProvider, child) {
          if (reportsProvider.isInitial) {
            return const Center(child: CircularProgressIndicator());
          }

          if (reportsProvider.hasError) {
            return CustomErrorWidget(
              message: reportsProvider.errorMessage ?? 'حدث خطأ غير متوقع',
              onRetry: _initializeData,
            );
          }

          return TabBarView(
            controller: _tabController,
            children: [ExcelGenerationTab(), LocalFilesTab()],
          );
        },
      ),
    );
  }
}
