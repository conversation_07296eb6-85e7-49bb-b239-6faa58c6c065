import 'package:flutter_test/flutter_test.dart';
import 'package:clockin/services/file_manager_service.dart';
import 'package:clockin/models/download_result.dart';
import 'dart:typed_data';

void main() {
  group('Excel Download Mobile Tests', () {
    late FileManagerService fileManagerService;

    setUp(() {
      fileManagerService = FileManagerService();
    });

    test('FileManagerService should be instantiated', () {
      expect(fileManagerService, isNotNull);
    });

    test('DownloadResult.excelSuccess should create correct result', () {
      final result = DownloadResult.excelSuccess(
        filename: 'test_report.xlsx',
        filePath: '/path/to/file.xlsx',
        content: 'Excel file saved successfully',
      );

      expect(result.success, isTrue);
      expect(result.isExcelFile, isTrue);
      expect(result.filename, equals('test_report.xlsx'));
      expect(result.filePath, equals('/path/to/file.xlsx'));
      expect(result.content, equals('Excel file saved successfully'));
    });

    test('DownloadResult.csvSuccess should create correct result', () {
      final result = DownloadResult.csvSuccess(
        filename: 'test_report.csv',
        content: 'CSV content here',
      );

      expect(result.success, isTrue);
      expect(result.isExcelFile, isFalse);
      expect(result.filename, equals('test_report.csv'));
      expect(result.content, equals('CSV content here'));
      expect(result.filePath, isNull);
    });

    test('DownloadResult.error should create correct result', () {
      final result = DownloadResult.error(
        filename: 'failed_report.xlsx',
        isExcelFile: true,
        errorMessage: 'Download failed',
      );

      expect(result.success, isFalse);
      expect(result.isExcelFile, isTrue);
      expect(result.filename, equals('failed_report.xlsx'));
      expect(result.errorMessage, equals('Download failed'));
      expect(result.content, equals(''));
    });

    test('formatFileSize should format correctly', () {
      expect(fileManagerService.formatFileSize(500), equals('500 B'));
      expect(fileManagerService.formatFileSize(1536), equals('1.5 KB'));
      expect(fileManagerService.formatFileSize(1572864), equals('1.5 MB'));
      expect(fileManagerService.formatFileSize(1610612736), equals('1.5 GB'));
    });

    // Test de simulation pour la sauvegarde Excel
    test('saveExcelFile should handle mock data', () async {
      // Créer des données de test
      final testData = Uint8List.fromList([1, 2, 3, 4, 5]);
      final filename = 'test_excel_${DateTime.now().millisecondsSinceEpoch}.xlsx';

      // Note: Ce test ne peut pas vraiment sauvegarder sur mobile dans l'environnement de test
      // mais il vérifie que la méthode peut être appelée sans erreur de compilation
      expect(() async {
        try {
          await fileManagerService.saveExcelFile(filename, testData);
        } catch (e) {
          // Attendu dans l'environnement de test car il n'y a pas de système de fichiers mobile
          print('Expected error in test environment: $e');
        }
      }, returnsNormally);
    });
  });

  group('Integration Tests', () {
    test('Excel download workflow should be complete', () {
      // Test du workflow complet
      
      // 1. Créer un résultat de téléchargement Excel réussi
      final downloadResult = DownloadResult.excelSuccess(
        filename: 'rapport_employes_2025-08-04.xlsx',
        filePath: '/storage/emulated/0/Download/ClockIn_Reports/rapport_employes_2025-08-04.xlsx',
        content: 'Fichier Excel téléchargé: rapport_employes_2025-08-04.xlsx (15.2 KB)',
      );

      expect(downloadResult.success, isTrue);
      expect(downloadResult.isExcelFile, isTrue);
      expect(downloadResult.filePath, isNotNull);
      expect(downloadResult.content.contains('Excel'), isTrue);

      // 2. Vérifier que le nom de fichier est correct
      expect(downloadResult.filename.endsWith('.xlsx'), isTrue);
      expect(downloadResult.filename.contains('rapport_employes'), isTrue);

      // 3. Vérifier que le chemin de fichier est dans le bon répertoire
      expect(downloadResult.filePath!.contains('ClockIn_Reports'), isTrue);
    });

    test('CSV download workflow should be complete', () {
      // Test du workflow CSV
      
      final csvContent = '''
ID,Nom,Email,Site,Date,Heure Début,Heure Fin,Durée
1,Ahmed Ben Ali,<EMAIL>,Chantier Ain Sebaa,04/08/2025,08:00,17:00,9.0
2,Fatima Zahra,<EMAIL>,Bureau Principal,04/08/2025,09:00,18:00,9.0
''';

      final downloadResult = DownloadResult.csvSuccess(
        filename: 'rapport_employes_2025-08-04.csv',
        content: csvContent,
      );

      expect(downloadResult.success, isTrue);
      expect(downloadResult.isExcelFile, isFalse);
      expect(downloadResult.content.contains('Ahmed Ben Ali'), isTrue);
      expect(downloadResult.content.contains('Fatima Zahra'), isTrue);
      expect(downloadResult.filePath, isNull);
    });
  });
}
