<?php

// Script pour vider le cache Laravel et tester l'endpoint
echo "=== VIDAGE DU CACHE ET TEST ===\n";

$clockinPath = 'C:/wamp64/www/clockin';

echo "1. VIDAGE DU CACHE LARAVEL:\n";
echo str_repeat("-", 40) . "\n";

// Commandes Laravel pour vider le cache
$commands = [
    'php artisan route:clear',
    'php artisan cache:clear',
    'php artisan config:clear',
    'php artisan view:clear'
];

foreach ($commands as $command) {
    echo "Exécution: $command\n";
    
    $output = [];
    $returnCode = 0;
    
    // Changer de répertoire et exécuter la commande
    exec("cd \"$clockinPath\" && $command 2>&1", $output, $returnCode);
    
    if ($returnCode === 0) {
        echo "✅ Succès\n";
    } else {
        echo "❌ Erreur (Code: $returnCode)\n";
        foreach ($output as $line) {
            echo "   $line\n";
        }
    }
}

echo "\n2. VÉRIFICATION DES ROUTES:\n";
echo str_repeat("-", 40) . "\n";

// Lister les routes pour vérifier que notre endpoint est bien enregistré
$output = [];
$returnCode = 0;
exec("cd \"$clockinPath\" && php artisan route:list --name=sites 2>&1", $output, $returnCode);

if ($returnCode === 0) {
    echo "Routes contenant 'sites':\n";
    foreach ($output as $line) {
        if (strpos($line, 'sites') !== false || strpos($line, 'GET') !== false || strpos($line, 'Method') !== false) {
            echo "$line\n";
        }
    }
} else {
    echo "❌ Erreur lors de la liste des routes\n";
    foreach ($output as $line) {
        echo "$line\n";
    }
}

echo "\n3. TEST DIRECT DE L'ENDPOINT:\n";
echo str_repeat("-", 40) . "\n";

// Tester l'endpoint avec le token que nous avons
$token = '************************************'; // Token de l'utilisateur ID 5

$assignedUrl = 'http://192.168.0.50:8000/api/sites/user-assigned';

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $assignedUrl);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Authorization: Bearer ' . $token,
    'Accept: application/json'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_VERBOSE, true);

$assignedResponse = curl_exec($ch);
$assignedHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curlError = curl_error($ch);
curl_close($ch);

echo "URL testée: $assignedUrl\n";
echo "Code de réponse: $assignedHttpCode\n";

if ($curlError) {
    echo "Erreur cURL: $curlError\n";
}

if ($assignedResponse) {
    $assignedData = json_decode($assignedResponse, true);
    if ($assignedData) {
        echo "Réponse JSON:\n";
        echo json_encode($assignedData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
        
        if ($assignedHttpCode === 200 && isset($assignedData['success']) && $assignedData['success']) {
            echo "\n🎉 SUCCÈS! L'endpoint fonctionne!\n";
            echo "Site assigné: {$assignedData['data']['name']}\n";
            echo "ID: {$assignedData['data']['id']}\n";
            echo "Coordonnées: {$assignedData['data']['latitude']}, {$assignedData['data']['longitude']}\n";
            
            echo "\n📱 INSTRUCTIONS POUR FLUTTER:\n";
            echo "1. Redémarrer l'application Flutter\n";
            echo "2. Se connecter avec:\n";
            echo "   Email: <EMAIL>\n";
            echo "   Mot de passe: password123\n";
            echo "3. L'application devrait maintenant afficher le site assigné\n";
            
        } else {
            echo "\n❌ Problème détecté:\n";
            if (isset($assignedData['message'])) {
                echo "Message: {$assignedData['message']}\n";
            }
            if (isset($assignedData['message_ar'])) {
                echo "Message AR: {$assignedData['message_ar']}\n";
            }
        }
    } else {
        echo "Réponse brute: $assignedResponse\n";
    }
} else {
    echo "❌ Aucune réponse reçue\n";
}

echo "\n4. ALTERNATIVE - TEST AVEC NOUVELLE CONNEXION:\n";
echo str_repeat("-", 40) . "\n";

// Nouvelle connexion pour obtenir un token frais
$loginUrl = 'http://192.168.0.50:8000/api/auth/login';
$loginData = [
    'email' => '<EMAIL>',
    'password' => 'password123'
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $loginUrl);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($loginData));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);

$loginResponse = curl_exec($ch);
$loginHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($loginHttpCode === 200) {
    $loginResponseData = json_decode($loginResponse, true);
    
    if (isset($loginResponseData['data']['token'])) {
        $freshToken = $loginResponseData['data']['token'];
        echo "✅ Nouvelle connexion réussie\n";
        echo "Token frais: " . substr($freshToken, 0, 30) . "...\n";
        
        // Test avec le token frais
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $assignedUrl);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $freshToken,
            'Accept: application/json'
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        $freshResponse = curl_exec($ch);
        $freshHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        echo "Test avec token frais - Code: $freshHttpCode\n";
        
        if ($freshResponse) {
            $freshData = json_decode($freshResponse, true);
            if ($freshData) {
                echo json_encode($freshData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
            } else {
                echo "Réponse: $freshResponse\n";
            }
        }
    }
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "TEST TERMINÉ\n";
echo str_repeat("=", 50) . "\n";
