import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:open_file/open_file.dart';
import 'package:share_plus/share_plus.dart';

/// Service pour la gestion des fichiers sur mobile
class FileManagerService {
  static const String _reportsFolder = 'ClockIn_Reports';

  /// Demander les permissions nécessaires
  Future<bool> requestStoragePermissions() async {
    try {
      debugPrint('FileManagerService: Requesting storage permissions');

      if (Platform.isAndroid) {
        // Pour Android 13+ (API 33+), nous n'avons plus besoin de WRITE_EXTERNAL_STORAGE
        final androidInfo = await _getAndroidVersion();
        if (androidInfo >= 33) {
          // Android 13+ utilise les permissions granulaires
          return true;
        } else {
          // Android 12 et moins
          final status = await Permission.storage.request();
          return status.isGranted;
        }
      } else if (Platform.isIOS) {
        // iOS n'a pas besoin de permissions spéciales pour les documents
        return true;
      }

      return true;
    } catch (e) {
      debugPrint('FileManagerService: Error requesting permissions: $e');
      return false;
    }
  }

  /// Obtenir la version Android
  Future<int> _getAndroidVersion() async {
    try {
      // Simulation - dans une vraie app, utilisez device_info_plus
      return 33; // Supposons Android 13+
    } catch (e) {
      return 30; // Fallback vers Android 11
    }
  }

  /// Sauvegarder un fichier Excel sur le téléphone
  Future<String?> saveExcelFile(String filename, Uint8List bytes) async {
    try {
      debugPrint('FileManagerService: Saving Excel file: $filename');

      // Demander les permissions
      final hasPermission = await requestStoragePermissions();
      if (!hasPermission) {
        throw Exception('Permissions de stockage refusées');
      }

      // Obtenir le répertoire approprié selon la plateforme
      Directory? directory;

      if (Platform.isAndroid) {
        // Pour Android, utiliser le répertoire Downloads
        directory = await _getAndroidDownloadsDirectory();
      } else if (Platform.isIOS) {
        // Pour iOS, utiliser le répertoire Documents
        directory = await getApplicationDocumentsDirectory();
      }

      if (directory == null) {
        throw Exception('Impossible d\'accéder au répertoire de stockage');
      }

      // Créer le dossier ClockIn_Reports s'il n'existe pas
      final reportsDir = Directory('${directory.path}/$_reportsFolder');
      if (!await reportsDir.exists()) {
        await reportsDir.create(recursive: true);
        debugPrint(
          'FileManagerService: Created reports directory: ${reportsDir.path}',
        );
      }

      // Sauvegarder le fichier
      final file = File('${reportsDir.path}/$filename');
      await file.writeAsBytes(bytes);

      debugPrint('FileManagerService: File saved successfully: ${file.path}');
      debugPrint('FileManagerService: File size: ${bytes.length} bytes');

      return file.path;
    } catch (e) {
      debugPrint('FileManagerService: Error saving Excel file: $e');
      rethrow;
    }
  }

  /// Obtenir le répertoire Downloads sur Android
  Future<Directory?> _getAndroidDownloadsDirectory() async {
    try {
      // Essayer d'abord le répertoire Downloads externe
      final externalDir = await getExternalStorageDirectory();
      if (externalDir != null) {
        // Naviguer vers le répertoire Downloads
        final downloadsPath = externalDir.path.replaceAll(
          '/Android/data/${_getPackageName()}/files',
          '/Download',
        );
        final downloadsDir = Directory(downloadsPath);

        if (await downloadsDir.exists()) {
          return downloadsDir;
        }
      }

      // Fallback vers le répertoire Documents de l'application
      return await getApplicationDocumentsDirectory();
    } catch (e) {
      debugPrint(
        'FileManagerService: Error getting Android downloads directory: $e',
      );
      return await getApplicationDocumentsDirectory();
    }
  }

  /// Obtenir le nom du package (simulation)
  String _getPackageName() {
    return 'com.example.clockin'; // Remplacez par votre vrai package name
  }

  /// Ouvrir un fichier Excel
  Future<bool> openExcelFile(String filePath) async {
    try {
      debugPrint('FileManagerService: Opening Excel file: $filePath');

      final result = await OpenFile.open(filePath);

      if (result.type == ResultType.done) {
        debugPrint('FileManagerService: File opened successfully');
        return true;
      } else {
        debugPrint(
          'FileManagerService: Failed to open file: ${result.message}',
        );
        return false;
      }
    } catch (e) {
      debugPrint('FileManagerService: Error opening Excel file: $e');
      return false;
    }
  }

  /// Partager un fichier Excel
  Future<bool> shareExcelFile(String filePath, String filename) async {
    try {
      debugPrint('FileManagerService: Sharing Excel file: $filePath');

      final file = File(filePath);
      if (!await file.exists()) {
        throw Exception('Le fichier n\'existe pas');
      }

      await Share.shareXFiles(
        [XFile(filePath)],
        text: 'Rapport ClockIn: $filename',
        subject: 'Rapport ClockIn',
      );

      debugPrint('FileManagerService: File shared successfully');
      return true;
    } catch (e) {
      debugPrint('FileManagerService: Error sharing Excel file: $e');
      return false;
    }
  }

  /// Lister les fichiers de rapports sauvegardés
  Future<List<FileInfo>> getSavedReports() async {
    try {
      debugPrint('FileManagerService: Getting saved reports');

      Directory? directory;

      if (Platform.isAndroid) {
        directory = await _getAndroidDownloadsDirectory();
      } else if (Platform.isIOS) {
        directory = await getApplicationDocumentsDirectory();
      }

      if (directory == null) {
        return [];
      }

      final reportsDir = Directory('${directory.path}/$_reportsFolder');
      if (!await reportsDir.exists()) {
        return [];
      }

      final files = await reportsDir.list().toList();
      final reportFiles = <FileInfo>[];

      for (final file in files) {
        if (file is File && file.path.endsWith('.xlsx')) {
          final stat = await file.stat();
          reportFiles.add(
            FileInfo(
              name: file.path.split('/').last,
              path: file.path,
              size: stat.size,
              modifiedDate: stat.modified,
            ),
          );
        }
      }

      // Trier par date de modification (plus récent en premier)
      reportFiles.sort((a, b) => b.modifiedDate.compareTo(a.modifiedDate));

      debugPrint(
        'FileManagerService: Found ${reportFiles.length} saved reports',
      );
      return reportFiles;
    } catch (e) {
      debugPrint('FileManagerService: Error getting saved reports: $e');
      return [];
    }
  }

  /// Supprimer un fichier de rapport
  Future<bool> deleteReport(String filePath) async {
    try {
      debugPrint('FileManagerService: Deleting report: $filePath');

      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        debugPrint('FileManagerService: Report deleted successfully');
        return true;
      }

      return false;
    } catch (e) {
      debugPrint('FileManagerService: Error deleting report: $e');
      return false;
    }
  }

  /// Formater la taille du fichier
  String formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024)
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
}

/// Informations sur un fichier
class FileInfo {
  final String name;
  final String path;
  final int size;
  final DateTime modifiedDate;

  FileInfo({
    required this.name,
    required this.path,
    required this.size,
    required this.modifiedDate,
  });
}
