import 'package:flutter_test/flutter_test.dart';
import 'package:clockin/services/excel_generator_service.dart';
import 'dart:typed_data';

void main() {
  group('Excel Format Tests', () {
    test('ExcelGeneratorService should generate valid Excel XML', () async {
      final startDate = DateTime(2025, 8, 1);
      final endDate = DateTime(2025, 8, 4);
      
      final excelData = await ExcelGeneratorService.generateExcelReport(
        reportType: 'all_employees',
        startDate: startDate,
        endDate: endDate,
      );
      
      expect(excelData, isNotNull);
      expect(excelData.length, greaterThan(0));
      
      // Convertir en string pour vérifier le contenu
      final content = String.fromCharCodes(excelData);
      
      // Vérifier que c'est du XML Excel valide
      expect(content.contains('<?xml version="1.0"'), isTrue);
      expect(content.contains('<Workbook'), isTrue);
      expect(content.contains('xmlns="urn:schemas-microsoft-com:office:spreadsheet"'), isTrue);
      expect(content.contains('<Worksheet'), isTrue);
      expect(content.contains('</Workbook>'), isTrue);
      
      print('Excel XML content length: ${content.length} characters');
      print('First 200 characters: ${content.substring(0, 200)}...');
    });

    test('Excel content should contain Arabic headers', () async {
      final startDate = DateTime(2025, 8, 1);
      final endDate = DateTime(2025, 8, 4);
      
      final excelData = await ExcelGeneratorService.generateExcelReport(
        reportType: 'all_employees',
        startDate: startDate,
        endDate: endDate,
      );
      
      final content = String.fromCharCodes(excelData);
      
      // Vérifier les en-têtes en arabe
      expect(content.contains('اسم الموظف'), isTrue);
      expect(content.contains('الموقع'), isTrue);
      expect(content.contains('التاريخ'), isTrue);
      expect(content.contains('وقت الدخول'), isTrue);
      expect(content.contains('وقت الخروج'), isTrue);
      expect(content.contains('إجمالي الساعات'), isTrue);
      
      print('Arabic headers found in Excel content ✓');
    });

    test('Excel should contain employee data', () async {
      final startDate = DateTime(2025, 8, 1);
      final endDate = DateTime(2025, 8, 4);
      
      final excelData = await ExcelGeneratorService.generateExcelReport(
        reportType: 'all_employees',
        startDate: startDate,
        endDate: endDate,
      );
      
      final content = String.fromCharCodes(excelData);
      
      // Vérifier que les données d'employés sont présentes
      expect(content.contains('أحمد محمد علي'), isTrue);
      expect(content.contains('فاطمة سالم أحمد'), isTrue);
      expect(content.contains('مكتب الرياض'), isTrue);
      expect(content.contains('مكتب جدة'), isTrue);
      expect(content.contains('08:00'), isTrue);
      expect(content.contains('17:00'), isTrue);
      
      print('Employee data found in Excel content ✓');
    });

    test('Excel should have proper styling', () async {
      final startDate = DateTime(2025, 8, 1);
      final endDate = DateTime(2025, 8, 4);
      
      final excelData = await ExcelGeneratorService.generateExcelReport(
        reportType: 'all_employees',
        startDate: startDate,
        endDate: endDate,
      );
      
      final content = String.fromCharCodes(excelData);
      
      // Vérifier les styles
      expect(content.contains('<Styles>'), isTrue);
      expect(content.contains('ss:ID="Header"'), isTrue);
      expect(content.contains('ss:ID="Title"'), isTrue);
      expect(content.contains('ss:Color="#4472C4"'), isTrue);
      expect(content.contains('ss:Bold="1"'), isTrue);
      
      print('Excel styling found ✓');
    });

    test('generateFileName should create unique names', () {
      final filename1 = ExcelGeneratorService.generateFileName('all_employees');
      final filename2 = ExcelGeneratorService.generateFileName('individual');
      final filename3 = ExcelGeneratorService.generateFileName('site');
      
      expect(filename1.contains('rapport_all_employees'), isTrue);
      expect(filename2.contains('rapport_individual'), isTrue);
      expect(filename3.contains('rapport_site'), isTrue);
      
      expect(filename1.endsWith('.xlsx'), isTrue);
      expect(filename2.endsWith('.xlsx'), isTrue);
      expect(filename3.endsWith('.xlsx'), isTrue);
      
      // Les noms doivent être différents (à cause du timestamp)
      expect(filename1, isNot(equals(filename2)));
      expect(filename2, isNot(equals(filename3)));
      
      print('Generated filenames:');
      print('- $filename1');
      print('- $filename2');
      print('- $filename3');
    });

    test('Excel should contain statistics', () async {
      final startDate = DateTime(2025, 8, 1);
      final endDate = DateTime(2025, 8, 4);
      
      final excelData = await ExcelGeneratorService.generateExcelReport(
        reportType: 'all_employees',
        startDate: startDate,
        endDate: endDate,
      );
      
      final content = String.fromCharCodes(excelData);
      
      // Vérifier les statistiques
      expect(content.contains('الإحصائيات'), isTrue);
      expect(content.contains('إجمالي الموظفين'), isTrue);
      expect(content.contains('إجمالي أيام العمل'), isTrue);
      
      print('Statistics section found in Excel ✓');
    });

    test('Excel should handle different report types', () async {
      final startDate = DateTime(2025, 8, 1);
      final endDate = DateTime(2025, 8, 4);
      
      // Test all employees report
      final allEmployeesData = await ExcelGeneratorService.generateExcelReport(
        reportType: 'all_employees',
        startDate: startDate,
        endDate: endDate,
      );
      final allEmployeesContent = String.fromCharCodes(allEmployeesData);
      expect(allEmployeesContent.contains('تقرير حضور جميع الموظفين'), isTrue);
      
      // Test individual report
      final individualData = await ExcelGeneratorService.generateExcelReport(
        reportType: 'individual',
        startDate: startDate,
        endDate: endDate,
        selectedEmployeeId: 1,
      );
      final individualContent = String.fromCharCodes(individualData);
      expect(individualContent.contains('تقرير حضور موظف محدد'), isTrue);
      
      // Test site report
      final siteData = await ExcelGeneratorService.generateExcelReport(
        reportType: 'site',
        startDate: startDate,
        endDate: endDate,
        selectedSiteId: 1,
      );
      final siteContent = String.fromCharCodes(siteData);
      expect(siteContent.contains('تقرير حضور موقع محدد'), isTrue);
      
      print('All report types generate correct titles ✓');
    });

    test('Excel file size should be reasonable', () async {
      final startDate = DateTime(2025, 8, 1);
      final endDate = DateTime(2025, 8, 4);
      
      final excelData = await ExcelGeneratorService.generateExcelReport(
        reportType: 'all_employees',
        startDate: startDate,
        endDate: endDate,
      );
      
      // Le fichier ne devrait pas être trop petit (moins de 1KB) ni trop gros (plus de 100KB)
      expect(excelData.length, greaterThan(1024)); // Plus de 1KB
      expect(excelData.length, lessThan(102400)); // Moins de 100KB
      
      print('Excel file size: ${(excelData.length / 1024).toStringAsFixed(2)} KB ✓');
    });
  });

  group('Excel Compatibility Tests', () {
    test('Excel XML should be Microsoft Office compatible', () async {
      final startDate = DateTime(2025, 8, 1);
      final endDate = DateTime(2025, 8, 4);
      
      final excelData = await ExcelGeneratorService.generateExcelReport(
        reportType: 'all_employees',
        startDate: startDate,
        endDate: endDate,
      );
      
      final content = String.fromCharCodes(excelData);
      
      // Vérifier la compatibilité Microsoft Office
      expect(content.contains('urn:schemas-microsoft-com:office:spreadsheet'), isTrue);
      expect(content.contains('urn:schemas-microsoft-com:office:office'), isTrue);
      expect(content.contains('urn:schemas-microsoft-com:office:excel'), isTrue);
      
      // Vérifier les propriétés du document
      expect(content.contains('<DocumentProperties'), isTrue);
      expect(content.contains('<Title>'), isTrue);
      expect(content.contains('<Author>ClockIn App</Author>'), isTrue);
      
      print('Microsoft Office compatibility verified ✓');
    });
  });
}
