import 'package:flutter/material.dart';
import 'package:clockin/services/reports_service.dart';
import 'package:clockin/constants/app_constants.dart';

/// Widget de test pour déboguer le problème de fallback en temps réel
/// À ajouter temporairement dans l'application pour identifier le problème exact
class TestFallbackWidget extends StatefulWidget {
  const TestFallbackWidget({super.key});

  @override
  State<TestFallbackWidget> createState() => _TestFallbackWidgetState();
}

class _TestFallbackWidgetState extends State<TestFallbackWidget> {
  final ReportsService _reportsService = ReportsService();
  String _testResult = 'Prêt à tester le fallback';
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Fallback Debug'),
        backgroundColor: Colors.red,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Info sur la configuration
            Card(
              color: Colors.blue.withOpacity(0.1),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Configuration de Test',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Text('URL: ${AppConstants.baseUrl}'),
                    Text('Endpoint pointage: ${AppConstants.pointageEndpoint}'),
                    const SizedBox(height: 8),
                    const Text(
                      'Ce test va reproduire exactement le scénario des logs pour identifier le problème.',
                      style: TextStyle(fontStyle: FontStyle.italic),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Bouton de test
            ElevatedButton(
              onPressed: _isLoading ? null : _testFallbackScenario,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: _isLoading
                  ? const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2,
                          ),
                        ),
                        SizedBox(width: 8),
                        Text('Test en cours...'),
                      ],
                    )
                  : const Text('🔍 Tester le Fallback pour Employé 5'),
            ),
            
            const SizedBox(height: 16),
            
            // Résultats détaillés
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Logs de Debug Détaillés',
                        style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      Expanded(
                        child: Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.black87,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: SingleChildScrollView(
                            child: Text(
                              _testResult,
                              style: const TextStyle(
                                fontFamily: 'monospace',
                                fontSize: 12,
                                color: Colors.greenAccent,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _testFallbackScenario() async {
    setState(() {
      _isLoading = true;
      _testResult = '🚀 Début du test de fallback...\n';
    });

    try {
      // Reproduire exactement le scénario des logs
      final startDate = DateTime.parse('2025-07-29');
      final endDate = DateTime.parse('2025-08-05');
      const employeeId = 5;

      _appendLog('📅 Période de test: ${startDate.toIso8601String().split('T')[0]} à ${endDate.toIso8601String().split('T')[0]}');
      _appendLog('👤 Employé ID: $employeeId (سماح زهراوي)');
      _appendLog('');

      _appendLog('🔄 Étape 1: Test de génération de rapport CSV...');
      
      // Tester la méthode complète qui échoue
      final result = await _reportsService.generateAndDownloadCSVReport(
        reportType: 'individual',
        startDate: startDate,
        endDate: endDate,
        employeeId: employeeId,
      );

      _appendLog('');
      _appendLog('📊 Résultat final:');
      if (result != null) {
        _appendLog('   ✅ Succès: ${result.success}');
        _appendLog('   📄 Contenu: ${result.content.length > 100 ? result.content.substring(0, 100) + "..." : result.content}');
        _appendLog('   📁 Fichier: ${result.filename}');
        _appendLog('   💾 Chemin: ${result.filePath ?? "N/A"}');
        
        if (result.success) {
          _appendLog('');
          _appendLog('🎉 LE FALLBACK FONCTIONNE MAINTENANT !');
        } else {
          _appendLog('');
          _appendLog('❌ Le fallback échoue encore. Vérifiez les logs ci-dessus.');
        }
      } else {
        _appendLog('   ❌ Résultat null');
        _appendLog('');
        _appendLog('❌ Échec complet du processus');
      }

    } catch (e, stackTrace) {
      _appendLog('');
      _appendLog('💥 Exception capturée:');
      _appendLog('   Erreur: $e');
      _appendLog('   Stack: ${stackTrace.toString().split('\n').take(5).join('\n')}');
    } finally {
      setState(() {
        _isLoading = false;
      });
      
      _appendLog('');
      _appendLog('🏁 Test terminé. Vérifiez les logs Flutter pour plus de détails.');
    }
  }

  void _appendLog(String message) {
    setState(() {
      _testResult += '$message\n';
    });
    
    // Aussi imprimer dans les logs Flutter pour comparaison
    print('TestFallback: $message');
  }
}

/// Instructions pour ajouter ce widget à l'application:
/// 
/// 1. Ajoutez ce fichier dans lib/widgets/debug/
/// 
/// 2. Dans votre écran admin ou main.dart, ajoutez un bouton:
/// 
/// FloatingActionButton(
///   onPressed: () {
///     Navigator.push(
///       context,
///       MaterialPageRoute(builder: (context) => const TestFallbackWidget()),
///     );
///   },
///   backgroundColor: Colors.red,
///   child: const Icon(Icons.bug_report),
/// )
/// 
/// 3. Compilez et testez l'application
/// 
/// 4. Comparez les logs de ce widget avec les logs Flutter
/// 
/// 5. Identifiez où exactement le processus échoue
