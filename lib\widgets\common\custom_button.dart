import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';

enum ButtonType {
  primary,
  secondary,
  outline,
  text,
  success,
  warning,
  error,
  filled,
  tonal,
}

class CustomButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final IconData? icon;
  final ButtonType type;
  final bool isLoading;
  final bool isExpanded;
  final EdgeInsetsGeometry? padding;
  final double? width;
  final double? height;

  const CustomButton({
    super.key,
    required this.text,
    this.onPressed,
    this.icon,
    this.type = ButtonType.primary,
    this.isLoading = false,
    this.isExpanded = true,
    this.padding,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    final isDisabled = onPressed == null || isLoading;
    
    Widget button;
    
    switch (type) {
      case ButtonType.primary:
        button = _buildElevatedButton(context, isDisabled);
        break;
      case ButtonType.secondary:
        button = _buildSecondaryButton(context, isDisabled);
        break;
      case ButtonType.outline:
        button = _buildOutlinedButton(context, isDisabled);
        break;
      case ButtonType.text:
        button = _buildTextButton(context, isDisabled);
        break;
      case ButtonType.success:
        button = _buildSuccessButton(context, isDisabled);
        break;
      case ButtonType.warning:
        button = _buildWarningButton(context, isDisabled);
        break;
      case ButtonType.error:
        button = _buildErrorButton(context, isDisabled);
        break;
      case ButtonType.filled:
        button = _buildFilledButton(context, isDisabled);
        break;
      case ButtonType.tonal:
        button = _buildTonalButton(context, isDisabled);
        break;
    }

    if (isExpanded) {
      return SizedBox(
        width: width ?? double.infinity,
        height: height ?? 48.0,
        child: button,
      );
    }

    return SizedBox(
      width: width,
      height: height ?? 48.0,
      child: button,
    );
  }

  Widget _buildElevatedButton(BuildContext context, bool isDisabled) {
    return ElevatedButton(
      onPressed: isDisabled ? null : onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: isDisabled ? AppColors.buttonDisabled : AppColors.buttonPrimary,
        foregroundColor: AppColors.textWhite,
        elevation: isDisabled ? 0 : 2,
        padding: padding ?? const EdgeInsets.symmetric(
          horizontal: AppConstants.defaultPadding,
          vertical: 12,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        ),
      ),
      child: _buildButtonContent(context),
    );
  }

  Widget _buildSecondaryButton(BuildContext context, bool isDisabled) {
    return ElevatedButton(
      onPressed: isDisabled ? null : onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: isDisabled ? AppColors.buttonDisabled : AppColors.buttonSecondary,
        foregroundColor: AppColors.textWhite,
        elevation: isDisabled ? 0 : 2,
        padding: padding ?? const EdgeInsets.symmetric(
          horizontal: AppConstants.defaultPadding,
          vertical: 12,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        ),
      ),
      child: _buildButtonContent(context),
    );
  }

  Widget _buildOutlinedButton(BuildContext context, bool isDisabled) {
    return OutlinedButton(
      onPressed: isDisabled ? null : onPressed,
      style: OutlinedButton.styleFrom(
        foregroundColor: isDisabled ? AppColors.textHint : AppColors.primaryBlue,
        side: BorderSide(
          color: isDisabled ? AppColors.borderLight : AppColors.primaryBlue,
        ),
        padding: padding ?? const EdgeInsets.symmetric(
          horizontal: AppConstants.defaultPadding,
          vertical: 12,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        ),
      ),
      child: _buildButtonContent(context),
    );
  }

  Widget _buildTextButton(BuildContext context, bool isDisabled) {
    return TextButton(
      onPressed: isDisabled ? null : onPressed,
      style: TextButton.styleFrom(
        foregroundColor: isDisabled ? AppColors.textHint : AppColors.primaryBlue,
        padding: padding ?? const EdgeInsets.symmetric(
          horizontal: AppConstants.defaultPadding,
          vertical: 12,
        ),
      ),
      child: _buildButtonContent(context),
    );
  }

  Widget _buildSuccessButton(BuildContext context, bool isDisabled) {
    return ElevatedButton(
      onPressed: isDisabled ? null : onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: isDisabled ? AppColors.buttonDisabled : AppColors.buttonSuccess,
        foregroundColor: AppColors.textWhite,
        elevation: isDisabled ? 0 : 2,
        padding: padding ?? const EdgeInsets.symmetric(
          horizontal: AppConstants.defaultPadding,
          vertical: 12,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        ),
      ),
      child: _buildButtonContent(context),
    );
  }

  Widget _buildWarningButton(BuildContext context, bool isDisabled) {
    return ElevatedButton(
      onPressed: isDisabled ? null : onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: isDisabled ? AppColors.buttonDisabled : AppColors.buttonWarning,
        foregroundColor: AppColors.textWhite,
        elevation: isDisabled ? 0 : 2,
        padding: padding ?? const EdgeInsets.symmetric(
          horizontal: AppConstants.defaultPadding,
          vertical: 12,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        ),
      ),
      child: _buildButtonContent(context),
    );
  }

  Widget _buildErrorButton(BuildContext context, bool isDisabled) {
    return ElevatedButton(
      onPressed: isDisabled ? null : onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: isDisabled ? AppColors.buttonDisabled : AppColors.buttonError,
        foregroundColor: AppColors.textWhite,
        elevation: isDisabled ? 0 : 2,
        padding: padding ?? const EdgeInsets.symmetric(
          horizontal: AppConstants.defaultPadding,
          vertical: 12,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        ),
      ),
      child: _buildButtonContent(context),
    );
  }

  Widget _buildFilledButton(BuildContext context, bool isDisabled) {
    return SizedBox(
      width: isExpanded ? double.infinity : width,
      height: height ?? 48,
      child: FilledButton(
        onPressed: isDisabled ? null : onPressed,
        style: FilledButton.styleFrom(
          backgroundColor: AppColors.primaryBlue,
          foregroundColor: AppColors.textWhite,
          padding: padding ?? const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: _buildButtonContent(context),
      ),
    );
  }

  Widget _buildTonalButton(BuildContext context, bool isDisabled) {
    return SizedBox(
      width: isExpanded ? double.infinity : width,
      height: height ?? 48,
      child: FilledButton.tonal(
        onPressed: isDisabled ? null : onPressed,
        style: FilledButton.styleFrom(
          backgroundColor: AppColors.surfaceBlue,
          foregroundColor: AppColors.primaryBlue,
          padding: padding ?? const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: _buildButtonContent(context),
      ),
    );
  }

  Widget _buildButtonContent(BuildContext context) {
    if (isLoading) {
      return const SizedBox(
        width: 20,
        height: 20,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.textWhite),
        ),
      );
    }

    if (icon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 20),
          const SizedBox(width: 8),
          Text(
            text,
            style: Theme.of(context).textTheme.labelLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      );
    }

    return Text(
      text,
      style: Theme.of(context).textTheme.labelLarge?.copyWith(
        fontWeight: FontWeight.w600,
      ),
    );
  }
}

// Specialized button widgets for common use cases
class PrimaryButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final IconData? icon;
  final bool isLoading;

  const PrimaryButton({
    super.key,
    required this.text,
    this.onPressed,
    this.icon,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return CustomButton(
      text: text,
      onPressed: onPressed,
      icon: icon,
      type: ButtonType.primary,
      isLoading: isLoading,
    );
  }
}

class SecondaryButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final IconData? icon;
  final bool isLoading;

  const SecondaryButton({
    super.key,
    required this.text,
    this.onPressed,
    this.icon,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return CustomButton(
      text: text,
      onPressed: onPressed,
      icon: icon,
      type: ButtonType.secondary,
      isLoading: isLoading,
    );
  }
}

class OutlineButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final IconData? icon;
  final bool isLoading;

  const OutlineButton({
    super.key,
    required this.text,
    this.onPressed,
    this.icon,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return CustomButton(
      text: text,
      onPressed: onPressed,
      icon: icon,
      type: ButtonType.outline,
      isLoading: isLoading,
    );
  }
}
