import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'api_response.g.dart';

@JsonSerializable(genericArgumentFactories: true)
class ApiResponse<T> extends Equatable {
  final bool success;
  final String message;
  @Json<PERSON>ey(name: 'message_ar')
  final String? messageAr;
  final T? data;
  final Map<String, dynamic>? meta;
  final Map<String, dynamic>? errors;
  final String? code;
  final String? timestamp;

  const ApiResponse({
    required this.success,
    required this.message,
    this.messageAr,
    this.data,
    this.meta,
    this.errors,
    this.code,
    this.timestamp,
  });

  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) =>
      _$ApiResponseFromJson(json, fromJsonT);

  Map<String, dynamic> toJson(Object Function(T value) toJsonT) =>
      _$ApiResponseToJson(this, toJsonT);

  ApiResponse<T> copyWith({
    bool? success,
    String? message,
    String? messageAr,
    T? data,
    Map<String, dynamic>? meta,
    Map<String, dynamic>? errors,
    String? code,
    String? timestamp,
  }) {
    return ApiResponse<T>(
      success: success ?? this.success,
      message: message ?? this.message,
      messageAr: messageAr ?? this.messageAr,
      data: data ?? this.data,
      meta: meta ?? this.meta,
      errors: errors ?? this.errors,
      code: code ?? this.code,
      timestamp: timestamp ?? this.timestamp,
    );
  }

  String get displayMessage => messageAr ?? message;
  bool get hasData => data != null;
  bool get hasErrors => errors != null && errors!.isNotEmpty;
  bool get hasMeta => meta != null && meta!.isNotEmpty;

  @override
  List<Object?> get props => [
        success,
        message,
        messageAr,
        data,
        meta,
        errors,
        code,
        timestamp,
      ];

  @override
  String toString() {
    return 'ApiResponse{success: $success, message: $message, hasData: $hasData}';
  }
}

@JsonSerializable(genericArgumentFactories: true)
class PaginatedResponse<T> extends Equatable {
  final List<T> data;
  @JsonKey(name: 'current_page')
  final int currentPage;
  @JsonKey(name: 'last_page')
  final int lastPage;
  @JsonKey(name: 'per_page')
  final int perPage;
  final int total;
  @JsonKey(name: 'from')
  final int? from;
  @JsonKey(name: 'to')
  final int? to;
  @JsonKey(name: 'next_page_url')
  final String? nextPageUrl;
  @JsonKey(name: 'prev_page_url')
  final String? prevPageUrl;

  const PaginatedResponse({
    required this.data,
    required this.currentPage,
    required this.lastPage,
    required this.perPage,
    required this.total,
    this.from,
    this.to,
    this.nextPageUrl,
    this.prevPageUrl,
  });

  factory PaginatedResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) =>
      _$PaginatedResponseFromJson(json, fromJsonT);

  Map<String, dynamic> toJson(Object Function(T value) toJsonT) =>
      _$PaginatedResponseToJson(this, toJsonT);

  PaginatedResponse<T> copyWith({
    List<T>? data,
    int? currentPage,
    int? lastPage,
    int? perPage,
    int? total,
    int? from,
    int? to,
    String? nextPageUrl,
    String? prevPageUrl,
  }) {
    return PaginatedResponse<T>(
      data: data ?? this.data,
      currentPage: currentPage ?? this.currentPage,
      lastPage: lastPage ?? this.lastPage,
      perPage: perPage ?? this.perPage,
      total: total ?? this.total,
      from: from ?? this.from,
      to: to ?? this.to,
      nextPageUrl: nextPageUrl ?? this.nextPageUrl,
      prevPageUrl: prevPageUrl ?? this.prevPageUrl,
    );
  }

  bool get hasNextPage => nextPageUrl != null;
  bool get hasPrevPage => prevPageUrl != null;
  bool get isFirstPage => currentPage == 1;
  bool get isLastPage => currentPage == lastPage;
  bool get isEmpty => data.isEmpty;
  bool get isNotEmpty => data.isNotEmpty;
  int get itemCount => data.length;

  String get pageInfo => 'صفحة $currentPage من $lastPage';
  String get itemsInfo {
    if (from != null && to != null) {
      return 'عرض $from إلى $to من أصل $total';
    }
    return 'إجمالي $total عنصر';
  }

  @override
  List<Object?> get props => [
        data,
        currentPage,
        lastPage,
        perPage,
        total,
        from,
        to,
        nextPageUrl,
        prevPageUrl,
      ];

  @override
  String toString() {
    return 'PaginatedResponse{itemCount: $itemCount, currentPage: $currentPage, total: $total}';
  }
}

@JsonSerializable()
class ErrorResponse extends Equatable {
  final bool success;
  final String message;
  @JsonKey(name: 'message_ar')
  final String? messageAr;
  final Map<String, List<String>>? errors;
  final String? code;
  final String? timestamp;

  const ErrorResponse({
    required this.success,
    required this.message,
    this.messageAr,
    this.errors,
    this.code,
    this.timestamp,
  });

  factory ErrorResponse.fromJson(Map<String, dynamic> json) =>
      _$ErrorResponseFromJson(json);

  Map<String, dynamic> toJson() => _$ErrorResponseToJson(this);

  String get displayMessage => messageAr ?? message;
  bool get hasValidationErrors => errors != null && errors!.isNotEmpty;

  List<String> get allErrorMessages {
    if (!hasValidationErrors) return [displayMessage];
    
    final List<String> messages = [];
    errors!.forEach((field, fieldErrors) {
      messages.addAll(fieldErrors);
    });
    return messages;
  }

  String get firstErrorMessage {
    if (!hasValidationErrors) return displayMessage;
    return errors!.values.first.first;
  }

  @override
  List<Object?> get props => [success, message, messageAr, errors, code, timestamp];

  @override
  String toString() {
    return 'ErrorResponse{success: $success, message: $message, hasValidationErrors: $hasValidationErrors}';
  }
}

@JsonSerializable()
class SuccessResponse extends Equatable {
  final bool success;
  final String message;
  @JsonKey(name: 'message_ar')
  final String? messageAr;
  final String? timestamp;

  const SuccessResponse({
    required this.success,
    required this.message,
    this.messageAr,
    this.timestamp,
  });

  factory SuccessResponse.fromJson(Map<String, dynamic> json) =>
      _$SuccessResponseFromJson(json);

  Map<String, dynamic> toJson() => _$SuccessResponseToJson(this);

  String get displayMessage => messageAr ?? message;

  @override
  List<Object?> get props => [success, message, messageAr, timestamp];

  @override
  String toString() {
    return 'SuccessResponse{success: $success, message: $message}';
  }
}

// Helper class for handling API exceptions
class ApiException implements Exception {
  final String message;
  final String? code;
  final int? statusCode;
  final Map<String, dynamic>? errors;

  const ApiException({
    required this.message,
    this.code,
    this.statusCode,
    this.errors,
  });

  factory ApiException.fromErrorResponse(ErrorResponse errorResponse, [int? statusCode]) {
    return ApiException(
      message: errorResponse.displayMessage,
      code: errorResponse.code,
      statusCode: statusCode,
      errors: errorResponse.errors,
    );
  }

  factory ApiException.network([String? message]) {
    return ApiException(
      message: message ?? 'خطأ في الاتصال بالشبكة',
      code: 'NETWORK_ERROR',
    );
  }

  factory ApiException.timeout([String? message]) {
    return ApiException(
      message: message ?? 'انتهت مهلة الاتصال',
      code: 'TIMEOUT_ERROR',
    );
  }

  factory ApiException.unauthorized([String? message]) {
    return ApiException(
      message: message ?? 'غير مخول للوصول',
      code: 'UNAUTHORIZED',
      statusCode: 401,
    );
  }

  factory ApiException.forbidden([String? message]) {
    return ApiException(
      message: message ?? 'ممنوع الوصول',
      code: 'FORBIDDEN',
      statusCode: 403,
    );
  }

  factory ApiException.notFound([String? message]) {
    return ApiException(
      message: message ?? 'المورد غير موجود',
      code: 'NOT_FOUND',
      statusCode: 404,
    );
  }

  factory ApiException.serverError([String? message]) {
    return ApiException(
      message: message ?? 'خطأ في الخادم',
      code: 'SERVER_ERROR',
      statusCode: 500,
    );
  }

  factory ApiException.unknown([String? message]) {
    return ApiException(
      message: message ?? 'حدث خطأ غير متوقع',
      code: 'UNKNOWN_ERROR',
    );
  }

  bool get isNetworkError => code == 'NETWORK_ERROR';
  bool get isTimeoutError => code == 'TIMEOUT_ERROR';
  bool get isUnauthorized => statusCode == 401;
  bool get isForbidden => statusCode == 403;
  bool get isNotFound => statusCode == 404;
  bool get isServerError => statusCode != null && statusCode! >= 500;
  bool get hasValidationErrors => errors != null && errors!.isNotEmpty;

  @override
  String toString() {
    return 'ApiException{message: $message, code: $code, statusCode: $statusCode}';
  }
}
