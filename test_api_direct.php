<?php

// Test the API endpoints directly by calling the controller methods
echo "🔧 Testing API Controllers Directly\n";
echo "===================================\n\n";

try {
    // Change to the Laravel directory
    chdir('C:\wamp64\www\clockin');
    
    // Include Laravel bootstrap
    require_once 'vendor/autoload.php';
    
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "✅ Laravel bootstrapped\n";
    
    // Create the controller instances using Laravel's service container
    $exportService = $app->make(\App\Services\ExportService::class);
    $notificationService = $app->make(\App\Services\NotificationService::class);
    $reportController = new \App\Http\Controllers\Report\ReportController($exportService, $notificationService);
    
    echo "✅ ReportController instantiated\n";
    
    // Create a mock request for individual report
    echo "🔄 Testing individual report endpoint...\n";
    
    $requestData = [
        'start_date' => '2025-07-28',
        'end_date' => '2025-08-04'
    ];
    
    // Create a Laravel request object
    $request = new \Illuminate\Http\Request();
    $request->merge($requestData);
    
    echo "📅 Request data: " . json_encode($requestData) . "\n";
    echo "👤 User ID: 5\n";
    
    // Call the controller method directly
    $response = $reportController->generateIndividualReport($request, 5);
    
    // Get the response data
    $responseData = $response->getData(true);
    $statusCode = $response->getStatusCode();
    
    echo "📊 Response Status: $statusCode\n";
    echo "📄 Response Data:\n";
    echo json_encode($responseData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
    
    if ($statusCode === 200 && isset($responseData['success']) && $responseData['success']) {
        echo "✅ Individual report API test successful!\n";
        echo "📁 Generated file: " . $responseData['data']['filename'] . "\n";
        echo "📏 File size: " . $responseData['data']['file_size'] . "\n";
        
        // Check if the file actually exists
        $downloadUrl = $responseData['data']['download_url'];
        $filename = basename($downloadUrl);
        $filePath = storage_path('app/' . $filename);
        
        if (file_exists($filePath)) {
            echo "✅ File exists on disk: " . filesize($filePath) . " bytes\n";
            
            // Clean up the test file
            unlink($filePath);
            echo "🧹 Test file cleaned up\n";
        } else {
            echo "❌ File not found on disk: $filePath\n";
        }
    } else {
        echo "❌ Individual report API test failed\n";
    }
    
    echo "\n🔄 Testing employee report endpoint...\n";
    
    // Test employee report (all employees)
    $response2 = $reportController->generateEmployeeReport($request);
    $responseData2 = $response2->getData(true);
    $statusCode2 = $response2->getStatusCode();
    
    echo "📊 Response Status: $statusCode2\n";
    
    if ($statusCode2 === 200 && isset($responseData2['success']) && $responseData2['success']) {
        echo "✅ Employee report API test successful!\n";
        echo "📁 Generated file: " . $responseData2['data']['filename'] . "\n";
        
        // Clean up the test file
        $filename2 = basename($responseData2['data']['download_url']);
        $filePath2 = storage_path('app/' . $filename2);
        if (file_exists($filePath2)) {
            unlink($filePath2);
            echo "🧹 Test file cleaned up\n";
        }
    } else {
        echo "❌ Employee report API test failed\n";
        echo "📄 Response: " . json_encode($responseData2, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
    }
    
    echo "\n🔄 Testing site report endpoint...\n";
    
    // Test site report
    $siteId = 2; // Using the site we found earlier
    $response3 = $reportController->generateSiteReport($request, $siteId);
    $responseData3 = $response3->getData(true);
    $statusCode3 = $response3->getStatusCode();
    
    echo "📊 Response Status: $statusCode3\n";
    
    if ($statusCode3 === 200 && isset($responseData3['success']) && $responseData3['success']) {
        echo "✅ Site report API test successful!\n";
        echo "📁 Generated file: " . $responseData3['data']['filename'] . "\n";
        
        // Clean up the test file
        $filename3 = basename($responseData3['data']['download_url']);
        $filePath3 = storage_path('app/' . $filename3);
        if (file_exists($filePath3)) {
            unlink($filePath3);
            echo "🧹 Test file cleaned up\n";
        }
    } else {
        echo "❌ Site report API test failed\n";
        echo "📄 Response: " . json_encode($responseData3, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "📍 File: " . $e->getFile() . " Line: " . $e->getLine() . "\n";
    echo "📋 Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n🏁 Direct API test completed!\n";
?>
