<?php

/**
 * Script de test pour vérifier le backend actuel à http://************:8000
 * Ce script teste les endpoints disponibles et identifie ce qui manque
 */

echo "🔍 Test du Backend Actuel - ClockIn\n";
echo "====================================\n\n";

// Configuration
$baseUrl = 'http://************:8000';
$testEmail = '<EMAIL>'; // Changez selon votre configuration
$testPassword = 'password'; // Changez selon votre configuration

echo "🌐 URL de base: $baseUrl\n";
echo "👤 Email de test: $testEmail\n\n";

// Fonction pour faire des requêtes HTTP
function makeRequest($url, $method = 'GET', $data = null, $headers = []) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    if ($method === 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        if ($data) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
    }
    
    if (!empty($headers)) {
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    curl_close($ch);
    
    return [
        'response' => $response,
        'http_code' => $httpCode,
        'error' => $error
    ];
}

// Test 1: Vérifier que le serveur répond
echo "📡 Test 1: Connexion au serveur\n";
echo "--------------------------------\n";

$result = makeRequest($baseUrl);

if ($result['error']) {
    echo "❌ Erreur de connexion: " . $result['error'] . "\n";
    echo "💡 Vérifiez que le serveur est démarré à cette adresse\n\n";
    exit(1);
} else {
    echo "✅ Serveur accessible (Code HTTP: " . $result['http_code'] . ")\n\n";
}

// Test 2: Test de l'endpoint d'authentification
echo "🔐 Test 2: Endpoint d'authentification\n";
echo "---------------------------------------\n";

$loginData = [
    'email' => $testEmail,
    'password' => $testPassword
];

$headers = [
    'Content-Type: application/json',
    'Accept: application/json'
];

$loginResult = makeRequest(
    $baseUrl . '/api/auth/login',
    'POST',
    $loginData,
    $headers
);

if ($loginResult['error']) {
    echo "❌ Erreur de connexion à l'API: " . $loginResult['error'] . "\n\n";
    exit(1);
} else {
    echo "📊 Code de réponse: " . $loginResult['http_code'] . "\n";
    
    $loginResponse = json_decode($loginResult['response'], true);
    
    if ($loginResponse && isset($loginResponse['success']) && $loginResponse['success']) {
        echo "✅ Authentification réussie\n";
        $token = $loginResponse['data']['token'] ?? null;
        
        if ($token) {
            echo "🔑 Token obtenu: " . substr($token, 0, 20) . "...\n\n";
            
            $authHeaders = [
                'Content-Type: application/json',
                'Accept: application/json',
                'Authorization: Bearer ' . $token
            ];
            
            // Test 3: Vérifier les endpoints de rapports existants
            echo "📊 Test 3: Endpoints de rapports existants\n";
            echo "-------------------------------------------\n";
            
            // Test génération rapport individuel (on sait que ça marche)
            echo "🧪 Test 3a: Génération rapport individuel\n";
            $reportData = [
                'start_date' => date('Y-m-d', strtotime('-7 days')),
                'end_date' => date('Y-m-d')
            ];
            
            $individualResult = makeRequest(
                $baseUrl . '/api/reports/employees/5',
                'POST',
                $reportData,
                $authHeaders
            );
            
            echo "📊 Code de réponse: " . $individualResult['http_code'] . "\n";
            if ($individualResult['http_code'] == 200) {
                echo "✅ Génération rapport individuel fonctionne\n";
                $individualResponse = json_decode($individualResult['response'], true);
                if ($individualResponse && isset($individualResponse['data']['filename'])) {
                    echo "📄 Fichier généré: " . $individualResponse['data']['filename'] . "\n";
                }
            } else {
                echo "❌ Génération rapport individuel échoue\n";
                echo "📄 Réponse: " . $individualResult['response'] . "\n";
            }
            
            echo "\n";
            
            // Test 3b: Vérifier l'endpoint attendance (nouveau)
            echo "🧪 Test 3b: Endpoint attendance (nouveau)\n";
            $attendanceUrl = $baseUrl . '/api/reports/attendance?' . http_build_query([
                'start_date' => date('Y-m-d', strtotime('-7 days')),
                'end_date' => date('Y-m-d'),
                'type' => 'all_employees'
            ]);
            
            $attendanceResult = makeRequest($attendanceUrl, 'GET', null, $authHeaders);
            
            echo "📊 Code de réponse: " . $attendanceResult['http_code'] . "\n";
            if ($attendanceResult['http_code'] == 200) {
                echo "✅ Endpoint attendance disponible\n";
                $attendanceResponse = json_decode($attendanceResult['response'], true);
                if ($attendanceResponse && isset($attendanceResponse['data']['attendance'])) {
                    echo "📈 Nombre d'enregistrements: " . count($attendanceResponse['data']['attendance']) . "\n";
                }
            } else {
                echo "❌ Endpoint attendance non disponible (attendu)\n";
                echo "📄 Réponse: " . $attendanceResult['response'] . "\n";
            }
            
            echo "\n";
            
            // Test 3c: Vérifier l'endpoint pointage (fallback)
            echo "🧪 Test 3c: Endpoint pointage (fallback)\n";
            $pointageUrl = $baseUrl . '/api/pointage?' . http_build_query([
                'start_date' => date('Y-m-d', strtotime('-7 days')),
                'end_date' => date('Y-m-d')
            ]);
            
            $pointageResult = makeRequest($pointageUrl, 'GET', null, $authHeaders);
            
            echo "📊 Code de réponse: " . $pointageResult['http_code'] . "\n";
            if ($pointageResult['http_code'] == 200) {
                echo "✅ Endpoint pointage disponible (fallback OK)\n";
                $pointageResponse = json_decode($pointageResult['response'], true);
                if ($pointageResponse && isset($pointageResponse['data'])) {
                    echo "📈 Nombre d'enregistrements: " . count($pointageResponse['data']) . "\n";
                } elseif ($pointageResponse && is_array($pointageResponse)) {
                    echo "📈 Nombre d'enregistrements: " . count($pointageResponse) . "\n";
                }
            } else {
                echo "❌ Endpoint pointage non disponible\n";
                echo "📄 Réponse: " . $pointageResult['response'] . "\n";
            }
            
        } else {
            echo "❌ Token non trouvé dans la réponse\n";
        }
    } else {
        echo "❌ Échec de l'authentification:\n";
        echo $loginResult['response'] . "\n";
    }
}

echo "\n🏁 Tests terminés!\n\n";

// Résumé et recommandations
echo "📋 Résumé et Recommandations:\n";
echo "==============================\n";
echo "1. ✓ Le serveur à $baseUrl est accessible\n";
echo "2. ✓ L'authentification fonctionne\n";
echo "3. ✓ La génération de rapports individuels fonctionne\n";
echo "4. ❓ L'endpoint /api/reports/attendance n'est probablement pas disponible\n";
echo "5. ❓ L'endpoint /api/pointage peut servir de fallback\n\n";

echo "💡 Solution recommandée:\n";
echo "- Le service Flutter a été modifié pour utiliser un fallback\n";
echo "- Si /api/reports/attendance n'existe pas, il utilisera /api/pointage\n";
echo "- Cela devrait permettre aux rapports de fonctionner avec le backend actuel\n\n";

echo "🚀 Prochaines étapes:\n";
echo "1. Testez l'application Flutter avec ces modifications\n";
echo "2. Si nécessaire, ajoutez l'endpoint /api/reports/attendance au backend\n";
echo "3. Vérifiez que les données de pointage sont correctement formatées\n";

?>
