import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:clockin/services/reports_service.dart';
import 'package:clockin/constants/app_constants.dart';

/// Test d'intégration pour vérifier que les rapports fonctionnent correctement
/// avec le backend à C:\wamp64\www\clockin
void main() {
  group('Reports Integration Tests', () {
    late ReportsService reportsService;

    setUp(() {
      reportsService = ReportsService();
    });

    test('should fetch all employees attendance data', () async {
      // Arrange
      final startDate = DateTime.now().subtract(Duration(days: 7));
      final endDate = DateTime.now();

      // Act
      try {
        final result = await reportsService.generateAndDownloadCSVReport(
          reportType: 'all_employees',
          startDate: startDate,
          endDate: endDate,
        );

        // Assert
        expect(result, isNotNull);
        if (result != null) {
          print('Test Result: ${result.success}');
          print('Content: ${result.content}');
          print('Filename: ${result.filename}');
          
          if (!result.success) {
            print('Error: ${result.content}');
          }
        }
      } catch (e) {
        print('Test Error: $e');
        fail('Exception thrown: $e');
      }
    });

    test('should generate employee report via API', () async {
      // Arrange
      final startDate = DateTime.now().subtract(Duration(days: 7));
      final endDate = DateTime.now();

      // Act
      try {
        final result = await reportsService.generateEmployeeReport(
          startDate: startDate.toIso8601String().split('T')[0],
          endDate: endDate.toIso8601String().split('T')[0],
          includeStats: true,
        );

        // Assert
        expect(result, isNotNull);
        expect(result.filename, isNotEmpty);
        print('Generated report: ${result.filename}');
        print('Download URL: ${result.downloadUrl}');
        print('File size: ${result.fileSize}');
      } catch (e) {
        print('API Test Error: $e');
        // Ne pas faire échouer le test si c'est un problème de connexion
        print('This might be expected if backend is not running');
      }
    });

    test('should verify API endpoints configuration', () {
      // Arrange & Act
      final baseUrl = AppConstants.baseUrl;
      final reportsEndpoint = AppConstants.reportsEndpoint;
      
      // Assert
      expect(baseUrl, equals('http://localhost/clockin/public'));
      expect(reportsEndpoint, equals('/api/reports'));
      
      print('Base URL: $baseUrl');
      print('Reports Endpoint: $reportsEndpoint');
      print('Full Reports URL: $baseUrl$reportsEndpoint');
    });
  });
}

/// Fonction utilitaire pour tester manuellement les endpoints
Future<void> testReportsEndpoints() async {
  print('🔍 Testing Reports Integration');
  print('================================');
  
  final reportsService = ReportsService();
  final startDate = DateTime.now().subtract(Duration(days: 7));
  final endDate = DateTime.now();
  
  print('📅 Testing period: ${startDate.toIso8601String().split('T')[0]} to ${endDate.toIso8601String().split('T')[0]}');
  
  // Test 1: Génération de rapport pour tous les employés
  print('\n📊 Test 1: All Employees Report');
  try {
    final result = await reportsService.generateAndDownloadCSVReport(
      reportType: 'all_employees',
      startDate: startDate,
      endDate: endDate,
    );
    
    if (result != null) {
      print('✅ Success: ${result.success}');
      print('📄 Content: ${result.content.length > 100 ? result.content.substring(0, 100) + "..." : result.content}');
      print('📁 Filename: ${result.filename}');
      if (result.filePath != null) {
        print('💾 File Path: ${result.filePath}');
      }
    } else {
      print('❌ Result is null');
    }
  } catch (e) {
    print('❌ Error: $e');
  }
  
  // Test 2: Test de l'endpoint d'API directement
  print('\n🌐 Test 2: Direct API Call');
  try {
    final report = await reportsService.generateEmployeeReport(
      startDate: startDate.toIso8601String().split('T')[0],
      endDate: endDate.toIso8601String().split('T')[0],
      includeStats: true,
    );
    
    print('✅ API Success');
    print('📄 Filename: ${report.filename}');
    print('🔗 Download URL: ${report.downloadUrl}');
    print('📏 File Size: ${report.fileSize}');
    print('⏰ Generated At: ${report.generatedAt}');
  } catch (e) {
    print('❌ API Error: $e');
  }
  
  print('\n🏁 Test completed!');
}

/// Widget de test pour l'interface utilisateur
class ReportsTestWidget extends StatefulWidget {
  @override
  _ReportsTestWidgetState createState() => _ReportsTestWidgetState();
}

class _ReportsTestWidgetState extends State<ReportsTestWidget> {
  final ReportsService _reportsService = ReportsService();
  String _testResult = 'Appuyez sur le bouton pour tester';
  bool _isLoading = false;

  Future<void> _runTest() async {
    setState(() {
      _isLoading = true;
      _testResult = 'Test en cours...';
    });

    try {
      final startDate = DateTime.now().subtract(Duration(days: 7));
      final endDate = DateTime.now();

      final result = await _reportsService.generateAndDownloadCSVReport(
        reportType: 'all_employees',
        startDate: startDate,
        endDate: endDate,
      );

      setState(() {
        if (result != null) {
          _testResult = '''
Test terminé!
Succès: ${result.success}
Contenu: ${result.content.length > 200 ? result.content.substring(0, 200) + "..." : result.content}
Nom de fichier: ${result.filename}
Chemin: ${result.filePath ?? 'N/A'}
          ''';
        } else {
          _testResult = 'Résultat null - Erreur de connexion ou de configuration';
        }
      });
    } catch (e) {
      setState(() {
        _testResult = 'Erreur: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Test d\'intégration des rapports'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Configuration API',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 8),
                    Text('URL de base: ${AppConstants.baseUrl}'),
                    Text('Endpoint rapports: ${AppConstants.reportsEndpoint}'),
                    Text('URL complète: ${AppConstants.baseUrl}${AppConstants.reportsEndpoint}'),
                  ],
                ),
              ),
            ),
            SizedBox(height: 16),
            ElevatedButton(
              onPressed: _isLoading ? null : _runTest,
              child: _isLoading
                  ? CircularProgressIndicator(color: Colors.white)
                  : Text('Tester l\'intégration des rapports'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(vertical: 16),
              ),
            ),
            SizedBox(height: 16),
            Expanded(
              child: Card(
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: SingleChildScrollView(
                    child: Text(
                      _testResult,
                      style: TextStyle(fontFamily: 'monospace'),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
