<?php

namespace App\Http\Controllers\Report;

use App\Http\Controllers\Controller;
use App\Http\Traits\ApiResponseTrait;
use App\Services\ExportService;
use App\Models\Pointage;
use App\Models\User;
use App\Models\Site;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

/**
 * @group Reports & Exports
 *
 * APIs pour la génération de rapports et exports Excel
 */
class ReportController extends Controller
{
    use ApiResponseTrait;

    private ExportService $exportService;

    public function __construct(ExportService $exportService)
    {
        $this->exportService = $exportService;
    }

    /**
     * Récupérer les données de présence pour les rapports
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getAttendanceData(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'type' => 'required|in:all_employees,individual,site',
            'employee_id' => 'nullable|integer|exists:users,id',
            'site_id' => 'nullable|integer|exists:sites,id',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse(
                'Données de validation invalides.',
                'بيانات التحقق غير صالحة.',
                422,
                $validator->errors()
            );
        }

        try {
            $startDate = Carbon::parse($request->start_date)->startOfDay();
            $endDate = Carbon::parse($request->end_date)->endOfDay();
            $type = $request->type;

            Log::info('Fetching attendance data', [
                'type' => $type,
                'start_date' => $startDate->toDateString(),
                'end_date' => $endDate->toDateString(),
                'employee_id' => $request->employee_id,
                'site_id' => $request->site_id,
            ]);

            $attendanceData = [];

            switch ($type) {
                case 'all_employees':
                    $attendanceData = $this->getAllEmployeesAttendance($startDate, $endDate);
                    break;
                case 'individual':
                    if ($request->employee_id) {
                        $attendanceData = $this->getEmployeeAttendance($request->employee_id, $startDate, $endDate);
                    }
                    break;
                case 'site':
                    if ($request->site_id) {
                        $attendanceData = $this->getSiteAttendance($request->site_id, $startDate, $endDate);
                    }
                    break;
            }

            return $this->successResponse([
                'attendance' => $attendanceData,
                'total_records' => count($attendanceData),
                'period' => [
                    'start' => $startDate->toDateString(),
                    'end' => $endDate->toDateString()
                ]
            ], 'Données de présence récupérées avec succès.', 'تم استرداد بيانات الحضور بنجاح.');

        } catch (\Exception $e) {
            Log::error('Error fetching attendance data', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
            ]);

            return $this->errorResponse(
                'Erreur lors de la récupération des données: ' . $e->getMessage(),
                'خطأ أثناء استرداد البيانات: ' . $e->getMessage(),
                500,
                $e
            );
        }
    }

    /**
     * Récupérer les données de présence de tous les employés
     */
    private function getAllEmployeesAttendance(Carbon $startDate, Carbon $endDate): array
    {
        $pointages = Pointage::with(['user', 'site'])
            ->whereBetween('debut_pointage', [$startDate, $endDate])
            ->orderBy('debut_pointage', 'desc')
            ->get();

        return $pointages->map(function ($pointage) {
            return $this->formatAttendanceData($pointage);
        })->toArray();
    }

    /**
     * Récupérer les données de présence d'un employé spécifique
     */
    private function getEmployeeAttendance(int $employeeId, Carbon $startDate, Carbon $endDate): array
    {
        $pointages = Pointage::with(['user', 'site'])
            ->where('user_id', $employeeId)
            ->whereBetween('debut_pointage', [$startDate, $endDate])
            ->orderBy('debut_pointage', 'desc')
            ->get();

        return $pointages->map(function ($pointage) {
            return $this->formatAttendanceData($pointage);
        })->toArray();
    }

    /**
     * Récupérer les données de présence d'un site spécifique
     */
    private function getSiteAttendance(int $siteId, Carbon $startDate, Carbon $endDate): array
    {
        $pointages = Pointage::with(['user', 'site'])
            ->where('site_id', $siteId)
            ->whereBetween('debut_pointage', [$startDate, $endDate])
            ->orderBy('debut_pointage', 'desc')
            ->get();

        return $pointages->map(function ($pointage) {
            return $this->formatAttendanceData($pointage);
        })->toArray();
    }

    /**
     * Formater les données de présence pour l'API
     */
    private function formatAttendanceData($pointage): array
    {
        $checkIn = $pointage->debut_pointage ? $pointage->debut_pointage->format('H:i') : '00:00';
        $checkOut = $pointage->fin_pointage ? $pointage->fin_pointage->format('H:i') : '00:00';
        $totalHours = $this->calculateTotalHours($pointage->debut_pointage, $pointage->fin_pointage);

        return [
            'id' => $pointage->id,
            'employee_name' => $pointage->user ? $pointage->user->name : 'غير محدد',
            'employee_id' => $pointage->user_id,
            'site_name' => $pointage->site ? $pointage->site->name : 'غير محدد',
            'site_id' => $pointage->site_id,
            'date' => $pointage->debut_pointage ? $pointage->debut_pointage->format('Y-m-d') : date('Y-m-d'),
            'check_in' => $checkIn,
            'check_out' => $checkOut,
            'total_hours' => $totalHours,
            'status' => $pointage->fin_pointage ? 'completed' : 'active',
        ];
    }

    /**
     * Calculer le total des heures travaillées
     */
    private function calculateTotalHours($checkIn, $checkOut): string
    {
        if (!$checkIn || !$checkOut) {
            return '0 ساعات';
        }

        $diff = $checkOut->diff($checkIn);
        $hours = $diff->h + ($diff->days * 24);
        $minutes = $diff->i;

        if ($minutes == 0) {
            return $hours . ' ساعات';
        } else {
            return $hours . ' ساعات و ' . $minutes . ' دقيقة';
        }
    }

    /**
     * Générer un rapport pour tous les employés
     */
    public function generateEmployeeReport(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'include_stats' => 'boolean',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse(
                'Données de validation invalides.',
                'بيانات التحقق غير صالحة.',
                422,
                $validator->errors()
            );
        }

        try {
            Log::info('Starting employee report generation', [
                'start_date' => $request->start_date,
                'end_date' => $request->end_date,
                'include_stats' => $request->boolean('include_stats', true)
            ]);

            $startDate = Carbon::parse($request->start_date)->startOfDay();
            $endDate = Carbon::parse($request->end_date)->endOfDay();

            $options = [
                'include_stats' => $request->boolean('include_stats', true)
            ];

            // Increase memory limit and execution time for report generation
            ini_set('memory_limit', '512M');
            ini_set('max_execution_time', 300); // 5 minutes

            $filePath = $this->exportService->generateEmployeeReport($startDate, $endDate, $options);
            
            if (!file_exists($filePath)) {
                throw new \Exception('Report file was not created');
            }

            $filename = basename($filePath);
            $fileSize = $this->formatFileSize(filesize($filePath));

            Log::info('Employee report generated successfully', [
                'filename' => $filename,
                'file_size' => $fileSize
            ]);

            return $this->successResponse([
                'filename' => $filename,
                'download_url' => "/api/reports/download/{$filename}",
                'file_size' => $fileSize,
                'generated_at' => now()->toISOString(),
                'period' => [
                    'start' => $startDate->toDateString(),
                    'end' => $endDate->toDateString()
                ]
            ], 'Rapport généré avec succès.', 'تم إنشاء التقرير بنجاح.');

        } catch (\Exception $e) {
            Log::error('Error generating employee report', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->errorResponse(
                'Erreur lors de la génération du rapport: ' . $e->getMessage(),
                'خطأ أثناء إنشاء التقرير: ' . $e->getMessage(),
                500,
                $e
            );
        }
    }

    /**
     * Générer un rapport pour un employé spécifique
     */
    public function generateIndividualReport(Request $request, int $userId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse(
                'Données de validation invalides.',
                'بيانات التحقق غير صالحة.',
                422,
                $validator->errors()
            );
        }

        try {
            // Check if user exists
            $user = User::find($userId);
            if (!$user) {
                return $this->errorResponse(
                    'Utilisateur non trouvé.',
                    'المستخدم غير موجود.',
                    404
                );
            }

            Log::info('Starting individual report generation', [
                'user_id' => $userId,
                'start_date' => $request->start_date,
                'end_date' => $request->end_date
            ]);

            $startDate = Carbon::parse($request->start_date)->startOfDay();
            $endDate = Carbon::parse($request->end_date)->endOfDay();

            // Increase memory limit and execution time for report generation
            ini_set('memory_limit', '512M');
            ini_set('max_execution_time', 300); // 5 minutes

            $filePath = $this->exportService->generateIndividualEmployeeReport($userId, $startDate, $endDate);

            if (!file_exists($filePath)) {
                throw new \Exception('Report file was not created');
            }

            $filename = basename($filePath);
            $fileSize = $this->formatFileSize(filesize($filePath));

            Log::info('Individual report generated successfully', [
                'user_id' => $userId,
                'filename' => $filename,
                'file_size' => $fileSize
            ]);

            return $this->successResponse([
                'filename' => $filename,
                'download_url' => "/api/reports/download/{$filename}",
                'file_size' => $fileSize,
                'generated_at' => now()->toISOString(),
                'user_id' => $userId
            ], 'Rapport individuel généré avec succès.', 'تم إنشاء التقرير الفردي بنجاح.');

        } catch (\Exception $e) {
            Log::error('Error generating individual report', [
                'user_id' => $userId,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->errorResponse(
                'Erreur lors de la génération du rapport: ' . $e->getMessage(),
                'خطأ أثناء إنشاء التقرير: ' . $e->getMessage(),
                500,
                $e
            );
        }
    }

    /**
     * Générer un rapport pour un site spécifique
     */
    public function generateSiteReport(Request $request, int $siteId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse(
                'Données de validation invalides.',
                'بيانات التحقق غير صالحة.',
                422,
                $validator->errors()
            );
        }

        try {
            Log::info('Starting site report generation', [
                'site_id' => $siteId,
                'start_date' => $request->start_date,
                'end_date' => $request->end_date
            ]);

            // Check if site exists
            $site = Site::find($siteId);
            if (!$site) {
                return $this->errorResponse(
                    'Site non trouvé.',
                    'الموقع غير موجود.',
                    404
                );
            }

            $startDate = Carbon::parse($request->start_date)->startOfDay();
            $endDate = Carbon::parse($request->end_date)->endOfDay();

            // Increase memory limit and execution time for report generation
            ini_set('memory_limit', '512M');
            ini_set('max_execution_time', 300); // 5 minutes

            $filePath = $this->exportService->generateSiteReport($siteId, $startDate, $endDate);

            if (!file_exists($filePath)) {
                throw new \Exception('Report file was not created');
            }

            $filename = basename($filePath);
            $fileSize = $this->formatFileSize(filesize($filePath));

            Log::info('Site report generated successfully', [
                'site_id' => $siteId,
                'filename' => $filename,
                'file_size' => $fileSize
            ]);

            return $this->successResponse([
                'filename' => $filename,
                'download_url' => "/api/reports/download/{$filename}",
                'file_size' => $fileSize,
                'generated_at' => now()->toISOString(),
                'site_id' => $siteId
            ], 'Rapport de site généré avec succès.', 'تم إنشاء تقرير الموقع بنجاح.');

        } catch (\Exception $e) {
            Log::error('Error generating site report', [
                'site_id' => $siteId,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->errorResponse(
                'Erreur lors de la génération du rapport: ' . $e->getMessage(),
                'خطأ أثناء إنشاء التقرير: ' . $e->getMessage(),
                500,
                $e
            );
        }
    }

    /**
     * Télécharger un rapport généré
     */
    public function downloadReport(string $filename): BinaryFileResponse|JsonResponse
    {
        try {
            $filePath = storage_path('app/reports/' . $filename);

            if (!file_exists($filePath)) {
                return $this->errorResponse(
                    'Fichier non trouvé.',
                    'الملف غير موجود.',
                    404
                );
            }

            Log::info('Downloading report file', [
                'filename' => $filename,
                'file_path' => $filePath,
                'file_size' => filesize($filePath)
            ]);

            return response()->download($filePath, $filename, [
                'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            ]);

        } catch (\Exception $e) {
            Log::error('Error downloading report', [
                'filename' => $filename,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
            ]);

            return $this->errorResponse(
                'Erreur lors du téléchargement: ' . $e->getMessage(),
                'خطأ أثناء التحميل: ' . $e->getMessage(),
                500,
                $e
            );
        }
    }

    /**
     * Formater la taille du fichier
     */
    private function formatFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        $bytes /= pow(1024, $pow);
        return round($bytes, 2) . ' ' . $units[$pow];
    }
}
