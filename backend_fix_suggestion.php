<?php

// SOLUTION POUR LE PROBLÈME DE SITE ASSIGNÉ
//
// PROBLÈME IDENTIFIÉ: La méthode getUserAssignedSite() dans PointageController
// utilise $this->cacheService->getUserPrimarySite($userId) qui ne trouve pas le site assigné
//
// SOLUTION 1: Ajouter un endpoint dans SiteController
// SOLUTION 2: Modifier la méthode getUserAssignedSite() dans PointageController

// ============ SOLUTION 1: Ajouter dans SiteController ============

/**
 * Récupère le site assigné à l'utilisateur connecté
 * 
 * @authenticated
 * 
 * @response 200 {
 *   "success": true,
 *   "data": {
 *     "id": 1,
 *     "name": "Chantier Sidi Bernoussi",
 *     "latitude": "33.57310000",
 *     "longitude": "-7.58980000",
 *     "rayon": 50
 *   }
 * }
 * 
 * @response 404 {
 *   "success": false,
 *   "message": "Aucun site assigné trouvé.",
 *   "message_ar": "لم يتم العثور على موقع مخصص."
 * }
 */
// public function getAssignedSite(Request $request): JsonResponse
{
    try {
        $user = $request->user();
        
        if (!$user) {
            return $this->errorResponse(
                'Utilisateur non authentifié.',
                'المستخدم غير مصادق عليه.',
                401
            );
        }

        // Méthode 1: Utiliser le default_site_id de l'utilisateur
        if ($user->default_site_id) {
            $site = Site::find($user->default_site_id);
            if ($site) {
                return $this->successResponse($site);
            }
        }

        // Méthode 2: Chercher dans la table pivot user_sites (si elle existe)
        $assignedSite = $user->sites()->first();
        if ($assignedSite) {
            return $this->successResponse($assignedSite);
        }

        // Méthode 3: Chercher dans une table d'assignation séparée
        $assignment = DB::table('site_assignments')
            ->where('user_id', $user->id)
            ->where('is_active', true)
            ->first();
            
        if ($assignment) {
            $site = Site::find($assignment->site_id);
            if ($site) {
                return $this->successResponse($site);
            }
        }

        // Aucun site trouvé
        return $this->errorResponse(
            'Aucun site assigné trouvé.',
            'لم يتم العثور على موقع مخصص.',
            404
        );

    } catch (\Exception $e) {
        LaravelLog::error('Erreur lors de la récupération du site assigné: ' . $e->getMessage());
        return $this->errorResponse(
            'Erreur lors de la récupération du site assigné.',
            'خطأ أثناء استرجاع الموقع المخصص.',
            500,
            $e
        );
    }
}

// Ajouter cette route dans routes/api.php :
// Route::get('/sites/assigned', [SiteController::class, 'getAssignedSite'])->middleware('auth:sanctum');

// Alternative: Modifier la méthode getUserAssignedSite dans PointageController.php
// private function getUserAssignedSite(int $userId): ?Site
{
    // Méthode 1: Utiliser le default_site_id
    $user = User::find($userId);
    if ($user && $user->default_site_id) {
        $site = Site::find($user->default_site_id);
        if ($site) {
            return $site;
        }
    }

    // Méthode 2: Relation many-to-many
    $site = Site::whereHas('users', function ($query) use ($userId) {
        $query->where('user_id', $userId);
    })->first();

    if ($site) {
        return $site;
    }

    // Méthode 3: Table d'assignation
    $assignment = DB::table('site_assignments')
        ->where('user_id', $userId)
        ->where('is_active', true)
        ->first();
        
    if ($assignment) {
        return Site::find($assignment->site_id);
    }

    return null;
}
