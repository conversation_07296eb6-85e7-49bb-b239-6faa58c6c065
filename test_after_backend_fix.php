<?php
// Test après correction backend
$loginUrl = "http://192.168.0.50:8000/api/auth/login";
$loginData = ["email" => "<EMAIL>", "password" => "password123"];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $loginUrl);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($loginData));
curl_setopt($ch, CURLOPT_HTTPHEADER, ["Content-Type: application/json"]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
$loginResponse = curl_exec($ch);
curl_close($ch);

$loginData = json_decode($loginResponse, true);
if (isset($loginData["data"]["token"])) {
    $token = $loginData["data"]["token"];
    
    $clientTime = new DateTime();
    $clientTimestamp = $clientTime->getTimestamp();
    
    echo "🕐 CLIENT TIME: " . $clientTime->format("H:i:s") . "\n";
    echo "📱 TIMESTAMP: $clientTimestamp\n";
    
    $pointageData = [
        "latitude" => 36.1331744,
        "longitude" => 4.7352042,
        "timestamp" => $clientTimestamp,
        "exact_time" => $clientTime->format("c")
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, "http://192.168.0.50:8000/api/pointage");
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($pointageData));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        "Authorization: Bearer $token",
        "Content-Type: application/json"
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $response = curl_exec($ch);
    curl_close($ch);
    
    $responseData = json_decode($response, true);
    if (isset($responseData["data"]["debut_pointage"])) {
        $backendTime = new DateTime($responseData["data"]["debut_pointage"]);
        $timeDiff = $backendTime->getTimestamp() - $clientTimestamp;
        
        echo "🕐 BACKEND TIME: " . $backendTime->format("H:i:s") . "\n";
        echo "⏱️ DIFFERENCE: $timeDiff seconds\n";
        
        if (abs($timeDiff) <= 1) {
            echo "✅ PARFAIT: Heure exacte préservée!\n";
        } else {
            echo "❌ PROBLÈME: Heure encore modifiée!\n";
        }
    }
}
unlink(__FILE__);
?>