import 'dart:convert';

/// Script de débogage pour identifier le problème exact avec le fallback
/// Simule exactement les données reçues selon les logs
void main() {
  print('🔍 Debug du Problème de Fallback');
  print('=================================\n');

  // Données exactes des logs (tronquées dans les logs mais reconstruites)
  final String backendResponseJson = '''
{
  "success": true,
  "data": [
    {
      "id": 46,
      "user": {
        "id": 5,
        "name": "سماح زهراوي",
        "email": "<EMAIL>",
        "role": null,
        "created_at": null,
        "updated_at": null
      },
      "site": {
        "id": 5,
        "name": "ain soltan",
        "latitude": null,
        "longitude": null,
        "created_at": null,
        "updated_at": null
      },
      "debut_pointage": "2025-08-03 00:13:29",
      "fin_pointage": "2025-08-03 00:13:54",
      "duree": "00:00:25",
      "debut_latitude": "36.13326110",
      "debut_longitude": "4.73511720",
      "fin_latitude": "36.13325990",
      "fin_longitude": "4.73511840",
      "is_active": false,
      "created_at": "2025-08-03 00:13:29",
      "updated_at": "2025-08-03 00:13:54"
    },
    {
      "id": 45,
      "user": {
        "id": 5,
        "name": "سماح زهراوي",
        "email": "<EMAIL>",
        "role": null,
        "created_at": null,
        "updated_at": null
      },
      "site": {
        "id": 5,
        "name": "ain soltan",
        "latitude": null,
        "longitude": null,
        "created_at": null,
        "updated_at": null
      },
      "debut_pointage": "2025-08-03 00:12:26",
      "fin_pointage": "2025-08-03 00:12:43",
      "duree": "00:00:17",
      "debut_latitude": "36.13326140",
      "debut_longitude": "4.73511480",
      "fin_latitude": "36.13326140",
      "fin_longitude": "4.73511470",
      "is_active": false,
      "created_at": "2025-08-03 00:12:26",
      "updated_at": "2025-08-03 00:12:43"
    }
  ]
}
  ''';

  try {
    print('📊 Étape 1: Parsing de la réponse JSON');
    final Map<String, dynamic> response = jsonDecode(backendResponseJson);
    print('✅ JSON parsé avec succès');
    print('   success: ${response['success']}');
    print('   data type: ${response['data'].runtimeType}');
    print('   data length: ${(response['data'] as List).length}');

    print('\n🔄 Étape 2: Simulation de la logique de fallback');
    
    // Simuler response.success && response.data != null
    final bool responseSuccess = response['success'] == true;
    final dynamic responseData = response['data'];
    final bool dataNotNull = responseData != null;
    
    print('   response.success: $responseSuccess');
    print('   response.data != null: $dataNotNull');
    print('   Condition (success && data != null): ${responseSuccess && dataNotNull}');
    
    if (responseSuccess && dataNotNull) {
      print('✅ Condition de fallback satisfaite');
      
      print('\n📋 Étape 3: Extraction des données');
      List<dynamic> rawData = [];
      
      if (responseData is List) {
        rawData = responseData;
        print('   Data is List, count: ${rawData.length}');
      } else if (responseData is Map) {
        final dataMap = responseData as Map<String, dynamic>;
        print('   Data is Map, keys: ${dataMap.keys.toList()}');
        
        if (dataMap.containsKey('data') && dataMap['data'] is List) {
          rawData = dataMap['data'];
          print('   Found data array, count: ${rawData.length}');
        } else {
          print('   No data array found in map');
        }
      }
      
      print('   Final rawData count: ${rawData.length}');
      
      if (rawData.isNotEmpty) {
        print('✅ Données extraites avec succès');
        
        print('\n🔄 Étape 4: Conversion des données');
        for (int i = 0; i < rawData.length; i++) {
          final item = rawData[i];
          print('   Converting item ${i + 1}:');
          print('     ID: ${item['id']}');
          print('     User: ${item['user']?['name']}');
          print('     Site: ${item['site']?['name']}');
          print('     Debut: ${item['debut_pointage']}');
          print('     Fin: ${item['fin_pointage']}');
          
          final converted = convertPointageDataDebug(item);
          print('     ✅ Converted:');
          print('       - Name: ${converted['name']}');
          print('       - Site: ${converted['site']}');
          print('       - Date: ${converted['date']}');
          print('       - CheckIn: ${converted['checkIn']}');
          print('       - CheckOut: ${converted['checkOut']}');
          print('       - Total: ${converted['totalHours']}');
          print('       - Status: ${converted['status']}');
        }
        
        print('\n🎉 Toutes les conversions réussies !');
        print('   Nombre d\'enregistrements convertis: ${rawData.length}');
        
      } else {
        print('❌ Aucune donnée extraite (rawData vide)');
      }
      
    } else {
      print('❌ Condition de fallback non satisfaite');
      print('   Ceci expliquerait le message "No employee attendance data received"');
    }
    
  } catch (e) {
    print('❌ Erreur lors du debug: $e');
    print('   Stack trace: ${StackTrace.current}');
  }
  
  print('\n📋 Résumé du Debug');
  print('==================');
  print('1. ✅ Les données JSON sont valides');
  print('2. ✅ La structure {"success": true, "data": [...]} est correcte');
  print('3. ✅ L\'extraction des données devrait fonctionner');
  print('4. ✅ La conversion des données fonctionne');
  print('');
  print('💡 Si le problème persiste, vérifiez:');
  print('   - Le type exact de response.data dans l\'app');
  print('   - Si response.success est vraiment true');
  print('   - Si une exception est levée pendant la conversion');
  print('   - Les logs détaillés ajoutés dans le code');
}

/// Version debug de la conversion avec logging détaillé
Map<String, dynamic> convertPointageDataDebug(dynamic item) {
  if (item is Map<String, dynamic>) {
    final debutPointage = item['debut_pointage'];
    final finPointage = item['fin_pointage'];
    final duree = item['duree'];
    final userId = item['user'];
    final siteData = item['site'];
    
    String checkIn = '00:00';
    String checkOut = '00:00';
    String date = DateTime.now().toIso8601String().split('T')[0];
    String totalHours = '0 ساعات';
    String userName = 'غير محدد';
    String siteName = 'غير محدد';
    
    // Extraire le nom de l'utilisateur
    if (userId is Map<String, dynamic> && userId['name'] != null) {
      userName = userId['name'].toString();
    }
    
    // Extraire le nom du site
    if (siteData is Map<String, dynamic> && siteData['name'] != null) {
      siteName = siteData['name'].toString();
    }
    
    if (debutPointage != null) {
      try {
        final debutDateTime = DateTime.parse(debutPointage.toString().replaceAll(' ', 'T'));
        checkIn = '${debutDateTime.hour.toString().padLeft(2, '0')}:${debutDateTime.minute.toString().padLeft(2, '0')}';
        date = debutDateTime.toIso8601String().split('T')[0];
      } catch (e) {
        print('       ⚠️ Error parsing debut_pointage: $e');
      }
    }
    
    if (finPointage != null) {
      try {
        final finDateTime = DateTime.parse(finPointage.toString().replaceAll(' ', 'T'));
        checkOut = '${finDateTime.hour.toString().padLeft(2, '0')}:${finDateTime.minute.toString().padLeft(2, '0')}';
      } catch (e) {
        print('       ⚠️ Error parsing fin_pointage: $e');
      }
    }
    
    // Utiliser la durée du backend si disponible
    if (duree != null && duree.toString().isNotEmpty) {
      totalHours = formatDurationDebug(duree.toString());
    } else {
      totalHours = calculateTotalHoursDebug(checkIn, checkOut);
    }
    
    return {
      'id': item['id'] ?? item['user_id'] ?? 0,
      'name': userName,
      'site': siteName,
      'date': date,
      'checkIn': checkIn,
      'checkOut': checkOut,
      'totalHours': totalHours,
      'status': finPointage != null ? 'completed' : 'active',
    };
  }

  return {
    'id': 0,
    'name': 'غير محدد',
    'site': 'غير محدد',
    'date': DateTime.now().toIso8601String().split('T')[0],
    'checkIn': '00:00',
    'checkOut': '00:00',
    'totalHours': '0 ساعات',
    'status': 'unknown',
  };
}

String formatDurationDebug(String duration) {
  try {
    final parts = duration.split(':');
    if (parts.length >= 3) {
      final hours = int.parse(parts[0]);
      final minutes = int.parse(parts[1]);
      final seconds = int.parse(parts[2]);
      
      if (hours > 0) {
        if (minutes > 0) {
          return '$hours ساعات و $minutes دقيقة';
        } else {
          return '$hours ساعات';
        }
      } else if (minutes > 0) {
        return '$minutes دقيقة';
      } else if (seconds > 0) {
        return '$seconds ثانية';
      }
    }
    return '0 ساعات';
  } catch (e) {
    return '0 ساعات';
  }
}

String calculateTotalHoursDebug(String checkIn, String checkOut) {
  try {
    if (checkIn.isEmpty || checkOut.isEmpty || checkIn == '00:00' || checkOut == '00:00') {
      return '0 ساعات';
    }

    final inParts = checkIn.split(':');
    final outParts = checkOut.split(':');

    final inMinutes = int.parse(inParts[0]) * 60 + int.parse(inParts[1]);
    final outMinutes = int.parse(outParts[0]) * 60 + int.parse(outParts[1]);

    final totalMinutes = outMinutes - inMinutes;

    if (totalMinutes <= 0) {
      return '0 ساعات';
    }

    final hours = totalMinutes ~/ 60;
    final minutes = totalMinutes % 60;

    if (minutes == 0) {
      return '$hours ساعات';
    } else {
      return '$hours ساعات و $minutes دقيقة';
    }
  } catch (e) {
    return '0 ساعات';
  }
}
