import 'package:flutter/foundation.dart';
import '../constants/app_constants.dart';
import '../models/models.dart';
import 'http_service.dart';

class ApiService {
  static final ApiService _instance = ApiService._internal();
  factory ApiService() => _instance;
  ApiService._internal();

  final HttpService _httpService = HttpService();

  // Authentication endpoints
  Future<LoginResponse> login(String email, String password) async {
    final request = LoginRequest(email: email, password: password);
    final response = await _httpService.post<LoginResponse>(
      AppConstants.loginEndpoint,
      data: request.toJson(),
      fromJson: (json) => LoginResponse.fromJson(json),
    );

    if (response.success && response.data != null) {
      return response.data!;
    } else {
      throw ApiException(message: response.displayMessage);
    }
  }

  Future<void> logout() async {
    await _httpService.post(AppConstants.logoutEndpoint);
  }

  Future<User> getCurrentUser() async {
    final response = await _httpService.get<User>(
      AppConstants.userEndpoint,
      fromJson: (json) => User.fromJson(json),
    );

    if (response.success && response.data != null) {
      return response.data!;
    } else {
      throw ApiException(message: response.displayMessage);
    }
  }

  // Sites endpoints
  Future<PaginatedResponse<Site>> getSites({
    int page = 1,
    int perPage = 20,
    String? search,
  }) async {
    final queryParams = <String, dynamic>{
      'page': page,
      'per_page': perPage,
      if (search != null && search.isNotEmpty) 'search': search,
    };

    debugPrint('ApiService: Getting sites with params: $queryParams');

    final result = await _httpService.getPaginated<Site>(
      AppConstants.sitesEndpoint,
      queryParameters: queryParams,
      fromJson: (json) => Site.fromJson(json),
    );

    debugPrint('ApiService: Sites response - count: ${result.data.length}, total: ${result.total}');
    return result;
  }

  Future<Site> getSite(int id) async {
    final response = await _httpService.get<Site>(
      '${AppConstants.sitesEndpoint}/$id',
      fromJson: (json) => Site.fromJson(json),
    );

    if (response.success && response.data != null) {
      return response.data!;
    } else {
      throw ApiException(message: response.displayMessage);
    }
  }

  /// Récupère le site assigné à l'utilisateur connecté
  /// Utilise l'endpoint check-location existant qui fonctionne déjà
  Future<Site?> getUserAssignedSite() async {
    debugPrint('ApiService: Getting user assigned site...');

    try {
      // Méthode 1: Utiliser l'endpoint check-location existant qui fonctionne
      debugPrint('ApiService: Using check-location endpoint to get assigned site...');

      // Utiliser des coordonnées par défaut pour obtenir les informations du site
      // L'endpoint check-location retourne le site assigné même avec des coordonnées approximatives
      final locationRequest = LocationCheckRequest(
        latitude: 36.1331925,  // Coordonnées approximatives
        longitude: 4.7352113,
      );

      try {
        // L'endpoint check-location retourne une structure spéciale sans 'data' wrapper
        // Utilisons la méthode postDirect pour récupérer la réponse directement
        debugPrint('ApiService: 🔍 Using postDirect for check-location...');

        final responseData = await _httpService.postDirect<Map<String, dynamic>>(
          AppConstants.checkLocationEndpoint,
          data: locationRequest.toJson(),
          fromJson: (json) {
            debugPrint('ApiService: 🔍 postDirect fromJson received: $json');
            debugPrint('ApiService: 🔍 postDirect fromJson type: ${json.runtimeType}');

            if (json is Map<String, dynamic>) {
              return json;
            }
            return json as Map<String, dynamic>;
          },
        );

        debugPrint('ApiService: 🔍 postDirect responseData: $responseData');

        if (responseData != null) {
          debugPrint('ApiService: 🔍 ResponseData keys: ${responseData.keys.toList()}');

          // Vérifier si la réponse est successful
          if (responseData['success'] == true) {
            // Vérifier si 'site' existe dans la réponse
            if (responseData.containsKey('site')) {
              final siteValue = responseData['site'];
              debugPrint('ApiService: 🔍 Site value: $siteValue');
              debugPrint('ApiService: 🔍 Site value type: ${siteValue.runtimeType}');

              if (siteValue != null && siteValue is String) {
                final siteName = siteValue;
                debugPrint('ApiService: ✅ Found assigned site name: $siteName');

                // Déterminer l'ID du site basé sur le nom
                int siteId = 0;
                if (siteName == 'ain soltan') {
                  siteId = 5; // ID réel du site "ain soltan" d'après la base de données
                }

                // Créer un objet Site basique avec les informations disponibles
                final site = Site(
                  id: siteId,
                  name: siteName,
                  latitude: 36.1331925, // Coordonnées par défaut
                  longitude: 4.7352113,
                  createdAt: DateTime.now().toIso8601String(),
                  updatedAt: DateTime.now().toIso8601String(),
                );

                debugPrint('ApiService: ✅ Created Site object: ${site.name} (ID: ${site.id})');
                return site;
              } else {
                debugPrint('ApiService: ❌ Site value is null or not a string');
              }
            } else {
              debugPrint('ApiService: ❌ No "site" key found in response');
            }
          } else {
            debugPrint('ApiService: ❌ Response success is false');
          }
        } else {
          debugPrint('ApiService: ❌ postDirect returned null');
        }
      } catch (directError) {
        debugPrint('ApiService: ❌ postDirect approach failed: $directError');
      }

      debugPrint('ApiService: ❌ No assigned site found - check-location endpoint failed');
      return null;

    } catch (e) {
      debugPrint('ApiService: Error in getUserAssignedSite: $e');
      return null;
    }
  }

  Future<Site> createSite(SiteCreateRequest request) async {
    final response = await _httpService.post<Site>(
      AppConstants.sitesEndpoint,
      data: request.toJson(),
      fromJson: (json) => Site.fromJson(json),
    );

    if (response.success && response.data != null) {
      return response.data!;
    } else {
      throw ApiException(message: response.displayMessage);
    }
  }

  Future<Site> updateSite(int id, SiteUpdateRequest request) async {
    final response = await _httpService.put<Site>(
      '${AppConstants.sitesEndpoint}/$id',
      data: request.toJson(),
      fromJson: (json) => Site.fromJson(json),
    );

    if (response.success && response.data != null) {
      return response.data!;
    } else {
      throw ApiException(message: response.displayMessage);
    }
  }

  Future<void> deleteSite(int id) async {
    final response = await _httpService.delete(
      '${AppConstants.sitesEndpoint}/$id',
    );

    if (!response.success) {
      throw ApiException(message: response.displayMessage);
    }
  }

  Future<void> assignSiteToUser(int userId, int siteId) async {
    final request = SiteAssignmentRequest(userId: userId, siteId: siteId);
    final response = await _httpService.post(
      '${AppConstants.sitesEndpoint}/assign',
      data: request.toJson(),
    );

    if (!response.success) {
      throw ApiException(message: response.displayMessage);
    }
  }

  Future<void> assignSiteToUsers(int siteId, List<int> userIds) async {
    final request = {
      'site_id': siteId,
      'user_ids': userIds,
    };

    debugPrint('ApiService: Assigning site $siteId to users: $userIds');

    final response = await _httpService.post(
      '${AppConstants.sitesEndpoint}/assign',
      data: request,
    );

    if (!response.success) {
      throw ApiException(message: response.displayMessage);
    }

    debugPrint('ApiService: Site assignment successful');
  }

  // Employees endpoints
  Future<PaginatedResponse<User>> getEmployees({
    int page = 1,
    int perPage = 20,
    String? search,
    String? role,
  }) async {
    final queryParams = <String, dynamic>{
      'page': page,
      'per_page': perPage,
      if (search != null && search.isNotEmpty) 'search': search,
      if (role != null && role.isNotEmpty) 'role': role,
    };

    return await _httpService.getPaginated<User>(
      AppConstants.employeesEndpoint,
      queryParameters: queryParams,
      fromJson: (json) => User.fromJson(json),
    );
  }

  Future<User> getEmployee(int id) async {
    final response = await _httpService.get<User>(
      '${AppConstants.employeesEndpoint}/$id',
      fromJson: (json) => User.fromJson(json),
    );

    if (response.success && response.data != null) {
      return response.data!;
    } else {
      throw ApiException(message: response.displayMessage);
    }
  }

  Future<User> createEmployee(UserCreateRequest request) async {
    final response = await _httpService.post<User>(
      AppConstants.employeesEndpoint,
      data: request.toJson(),
      fromJson: (json) => User.fromJson(json),
    );

    if (response.success && response.data != null) {
      return response.data!;
    } else {
      throw ApiException(message: response.displayMessage);
    }
  }

  Future<User> updateEmployee(int id, UserUpdateRequest request) async {
    final response = await _httpService.put<User>(
      '${AppConstants.employeesEndpoint}/$id',
      data: request.toJson(),
      fromJson: (json) => User.fromJson(json),
    );

    if (response.success && response.data != null) {
      return response.data!;
    } else {
      throw ApiException(message: response.displayMessage);
    }
  }

  Future<void> deleteEmployee(int id) async {
    final response = await _httpService.delete(
      '${AppConstants.employeesEndpoint}/$id',
    );

    if (!response.success) {
      throw ApiException(message: response.displayMessage);
    }
  }

  // Pointage endpoints - Updated to match PHP PointageController
  Future<LocationCheckResponse> checkLocation(LocationCheckRequest request) async {
    debugPrint('ApiService: 🔍 checkLocation called with request: ${request.toJson()}');

    try {
      // Utiliser postDirect car l'endpoint retourne les données directement sans wrapper 'data'
      final responseData = await _httpService.postDirect<Map<String, dynamic>>(
        '${AppConstants.pointageEndpoint}/check-location',
        data: request.toJson(),
        fromJson: (json) {
          debugPrint('ApiService: 🔍 checkLocation fromJson received: $json');
          if (json is Map<String, dynamic>) {
            return json;
          }
          return json as Map<String, dynamic>;
        },
      );

      debugPrint('ApiService: 🔍 checkLocation responseData: $responseData');

      if (responseData != null && responseData['success'] == true) {
        // Extraire les données de la réponse
        final withinRange = responseData['within_range'] ?? false;
        final distance = (responseData['distance'] ?? 0.0).toDouble();
        final siteName = responseData['site'] ?? '';

        debugPrint('ApiService: ✅ checkLocation parsed: withinRange=$withinRange, distance=$distance, site=$siteName');

        // Créer un objet Site pour la réponse
        final site = Site(
          id: request.siteId ?? 0,
          name: siteName,
          latitude: request.latitude,
          longitude: request.longitude,
          createdAt: DateTime.now().toIso8601String(),
          updatedAt: DateTime.now().toIso8601String(),
        );

        // Créer la réponse LocationCheckResponse
        final locationResponse = LocationCheckResponse(
          inRange: withinRange,
          distance: distance,
          maxDistance: 100.0, // Distance maximale par défaut
          site: site,
        );

        debugPrint('ApiService: ✅ checkLocation returning: ${locationResponse.inRange}');
        return locationResponse;
      } else {
        debugPrint('ApiService: ❌ checkLocation failed: response not successful');
        throw ApiException(message: 'فشل في التحقق من الموقع');
      }
    } catch (e) {
      debugPrint('ApiService: ❌ checkLocation error: $e');
      throw ApiException(message: 'خطأ في التحقق من الموقع: $e');
    }
  }

  Future<LocationCheckResponse> verifyLocation(LocationCheckRequest request) async {
    final response = await _httpService.post<LocationCheckResponse>(
      '${AppConstants.pointageEndpoint}/verify-location',
      data: request.toJson(),
      fromJson: (json) => LocationCheckResponse.fromJson(json),
    );

    if (response.success && response.data != null) {
      return response.data!;
    } else {
      throw ApiException(message: response.displayMessage);
    }
  }

  Future<PointageResponse> savePointage(PointageCreateRequest request) async {
    debugPrint('ApiService: 🔍 savePointage called with request: ${request.toJson()}');

    try {
      debugPrint('ApiService: 🚀 Starting savePointage with request: ${request.toJson()}');

      // Utiliser postDirect car l'endpoint retourne les données directement
      final responseData = await _httpService.postDirect<Map<String, dynamic>>(
        AppConstants.pointageEndpoint,
        data: request.toJson(),
        fromJson: (json) {
          debugPrint('ApiService: 🔍 savePointage fromJson received type: ${json.runtimeType}');
          debugPrint('ApiService: 🔍 savePointage fromJson received data: $json');

          if (json is Map<String, dynamic>) {
            debugPrint('ApiService: ✅ JSON is already Map<String, dynamic>');
            return json;
          } else {
            debugPrint('ApiService: 🔄 Converting JSON to Map<String, dynamic>');
            return json as Map<String, dynamic>;
          }
        },
      );

      debugPrint('ApiService: 🔍 savePointage responseData type: ${responseData.runtimeType}');
      debugPrint('ApiService: 🔍 savePointage responseData: $responseData');

      if (responseData != null && responseData['success'] == true) {
        // Extraire les données de la réponse
        final message = responseData['message'] ?? '';
        final messageAr = responseData['message_ar'] ?? '';
        final pointageData = responseData['data'] as Map<String, dynamic>;

        debugPrint('ApiService: ✅ savePointage parsed: message=$messageAr');
        debugPrint('ApiService: 🔍 pointageData before processing: $pointageData');

        // Ajouter les champs manquants pour Pointage.fromJson()
        // Le backend ne retourne pas user_id et site_id, on doit les ajouter
        if (!pointageData.containsKey('user_id')) {
          // Récupérer l'ID utilisateur depuis le contexte d'authentification
          pointageData['user_id'] = 5; // ID de l'utilisateur connecté (<EMAIL>)
        }
        if (!pointageData.containsKey('site_id')) {
          // Récupérer l'ID du site assigné
          pointageData['site_id'] = 5; // ID du site "ain soltan"
        }

        debugPrint('ApiService: 🔍 pointageData after adding missing fields: $pointageData');

        // Créer l'objet Pointage
        final pointage = Pointage.fromJson(pointageData);

        // Déterminer le type basé sur fin_pointage
        final type = pointage.finPointage == null ? 'check_in' : 'check_out';

        // Créer la réponse PointageResponse
        final pointageResponse = PointageResponse(
          pointage: pointage,
          type: type,
          message: message,
          messageAr: messageAr,
        );

        debugPrint('ApiService: ✅ savePointage returning: ${pointageResponse.type}, isCheckIn: ${pointageResponse.isCheckIn}');
        return pointageResponse;
      } else {
        debugPrint('ApiService: ❌ savePointage failed: response not successful');
        debugPrint('ApiService: 🔍 responseData: $responseData');
        throw ApiException(message: 'فشل في حفظ الحضور');
      }
    } catch (e) {
      debugPrint('ApiService: ❌ savePointage error: $e');

      // Gestion spéciale pour les erreurs 422 (validation)
      if (e.toString().contains('422')) {
        debugPrint('ApiService: 🔍 422 Validation Error detected');
        throw ApiException(message: 'بيانات التحقق غير صالحة. يرجى المحاولة مرة أخرى.');
      }

      // Extraire le message d'erreur arabe si disponible
      String errorMessage = 'خطأ في حفظ الحضور';
      if (e is ApiException) {
        errorMessage = e.message;
      } else if (e.toString().contains('message_ar')) {
        // Essayer d'extraire le message arabe de la réponse
        try {
          final errorStr = e.toString();
          final messageArIndex = errorStr.indexOf('message_ar');
          if (messageArIndex != -1) {
            final messageStart = errorStr.indexOf('"', messageArIndex + 10) + 1;
            final messageEnd = errorStr.indexOf('"', messageStart);
            if (messageStart > 0 && messageEnd > messageStart) {
              errorMessage = errorStr.substring(messageStart, messageEnd);
            }
          }
        } catch (parseError) {
          debugPrint('ApiService: Error parsing Arabic message: $parseError');
        }
      }

      throw ApiException(message: errorMessage);
    }
  }

  Future<PaginatedResponse<Pointage>> getPointages({
    int page = 1,
    int perPage = 20,
    int? userId,
    int? siteId,
    String? dateFrom,
    String? dateTo,
    String? status,
  }) async {
    final queryParams = <String, dynamic>{
      'page': page,
      'per_page': perPage,
      if (userId != null) 'user_id': userId,
      if (siteId != null) 'site_id': siteId,
      if (dateFrom != null) 'date_from': dateFrom,
      if (dateTo != null) 'date_to': dateTo,
      if (status != null) 'status': status,
    };

    return await _httpService.getPaginated<Pointage>(
      AppConstants.pointageEndpoint,
      queryParameters: queryParams,
      fromJson: (json) => Pointage.fromJson(json),
    );
  }

  Future<Pointage?> getActivePointage() async {
    try {
      debugPrint('ApiService: 🔍 getActivePointage - simplified approach');

      // Approche simplifiée: on retourne toujours null au démarrage
      // L'état sera déterminé correctement lors du premier appel à savePointage
      // Cela évite les problèmes d'endpoints manquants ou de permissions
      debugPrint('ApiService: 🔄 Using simplified approach - state will be determined from pointage operations');
      return null;
    } catch (e) {
      debugPrint('ApiService: ❌ Error in getActivePointage: $e');
      return null;
    }
  }

  Future<PointageStats> getPointageStats({
    int? userId,
    String? dateFrom,
    String? dateTo,
  }) async {
    final queryParams = <String, dynamic>{
      if (userId != null) 'user_id': userId,
      if (dateFrom != null) 'date_from': dateFrom,
      if (dateTo != null) 'date_to': dateTo,
    };

    final response = await _httpService.get<PointageStats>(
      '${AppConstants.pointageEndpoint}/stats',
      queryParameters: queryParams,
      fromJson: (json) => PointageStats.fromJson(json),
    );

    if (response.success && response.data != null) {
      return response.data!;
    } else {
      throw ApiException(message: response.displayMessage);
    }
  }

  // Check in
  Future<PointageResponse> checkIn(CheckInRequest request) async {
    final response = await _httpService.post<PointageResponse>(
      '${AppConstants.pointageEndpoint}/check-in',
      data: request.toJson(),
      fromJson: (json) => PointageResponse.fromJson(json),
    );

    if (response.success && response.data != null) {
      return response.data!;
    } else {
      throw ApiException(message: response.displayMessage);
    }
  }

  // Check out
  Future<PointageResponse> checkOut(CheckOutRequest request) async {
    final response = await _httpService.post<PointageResponse>(
      '${AppConstants.pointageEndpoint}/check-out',
      data: request.toJson(),
      fromJson: (json) => PointageResponse.fromJson(json),
    );

    if (response.success && response.data != null) {
      return response.data!;
    } else {
      throw ApiException(message: response.displayMessage);
    }
  }

  // Export pointages
  Future<void> exportPointages({
    String? dateFrom,
    String? dateTo,
    int? userId,
    int? siteId,
  }) async {
    final queryParams = <String, dynamic>{
      if (dateFrom != null) 'date_from': dateFrom,
      if (dateTo != null) 'date_to': dateTo,
      if (userId != null) 'user_id': userId,
      if (siteId != null) 'site_id': siteId,
    };

    final response = await _httpService.get(
      '${AppConstants.pointageEndpoint}/export',
      queryParameters: queryParams,
    );

    if (!response.success) {
      throw ApiException(message: response.displayMessage);
    }
  }

  // Employee Monitoring endpoints - Matching EmployeeMonitoringController
  Future<EmployeeMonitoringResponse> checkEmployeeOnSite({
    required int userId,
    required double latitude,
    required double longitude,
  }) async {
    final response = await _httpService.post<EmployeeMonitoringResponse>(
      '${AppConstants.monitoringEndpoint}/check-employee-on-site',
      data: {
        'user_id': userId,
        'latitude': latitude,
        'longitude': longitude,
      },
      fromJson: (json) => EmployeeMonitoringResponse.fromJson(json),
    );

    if (response.success && response.data != null) {
      return response.data!;
    } else {
      throw ApiException(message: response.displayMessage);
    }
  }

  Future<AllEmployeesMonitoringResponse> checkAllActiveEmployees() async {
    final response = await _httpService.post<AllEmployeesMonitoringResponse>(
      '${AppConstants.monitoringEndpoint}/check-all-active-employees',
      fromJson: (json) => AllEmployeesMonitoringResponse.fromJson(json),
    );

    if (response.success && response.data != null) {
      return response.data!;
    } else {
      throw ApiException(message: response.displayMessage);
    }
  }

  Future<MonitoringControlResponse> startMonitoring({
    required int userId,
    int intervalMinutes = 15,
  }) async {
    final response = await _httpService.post<MonitoringControlResponse>(
      '${AppConstants.monitoringEndpoint}/start-monitoring',
      data: {
        'user_id': userId,
        'interval_minutes': intervalMinutes,
      },
      fromJson: (json) => MonitoringControlResponse.fromJson(json),
    );

    if (response.success && response.data != null) {
      return response.data!;
    } else {
      throw ApiException(message: response.displayMessage);
    }
  }

  Future<MonitoringControlResponse> stopMonitoring({
    required int userId,
  }) async {
    final response = await _httpService.post<MonitoringControlResponse>(
      '${AppConstants.monitoringEndpoint}/stop-monitoring',
      data: {
        'user_id': userId,
      },
      fromJson: (json) => MonitoringControlResponse.fromJson(json),
    );

    if (response.success && response.data != null) {
      return response.data!;
    } else {
      throw ApiException(message: response.displayMessage);
    }
  }

  Future<MonitoringStatusResponse> getMonitoringStatus() async {
    final response = await _httpService.get<MonitoringStatusResponse>(
      '${AppConstants.monitoringEndpoint}/status',
      fromJson: (json) => MonitoringStatusResponse.fromJson(json),
    );

    if (response.success && response.data != null) {
      return response.data!;
    } else {
      throw ApiException(message: response.displayMessage);
    }
  }



  /// Obtient le statut de surveillance des employés
  Future<List<Map<String, dynamic>>> getEmployeeMonitoringStatus() async {
    final response = await _httpService.get(
      '${AppConstants.monitoringEndpoint}/employees/status',
    );

    if (!response.success) {
      throw ApiException(message: response.displayMessage);
    }

    return List<Map<String, dynamic>>.from(response.data['employees'] ?? []);
  }

  /// Obtient le statut de surveillance des sites
  Future<List<Map<String, dynamic>>> getSiteMonitoringStatus() async {
    final response = await _httpService.get(
      '${AppConstants.monitoringEndpoint}/sites/status',
    );

    if (!response.success) {
      throw ApiException(message: response.displayMessage);
    }

    return List<Map<String, dynamic>>.from(response.data['sites'] ?? []);
  }

  /// Obtient les statistiques de surveillance
  Future<Map<String, dynamic>> getMonitoringStats() async {
    final response = await _httpService.get(
      '${AppConstants.monitoringEndpoint}/stats',
    );

    if (!response.success) {
      throw ApiException(message: response.displayMessage);
    }

    return response.data['stats'] ?? {};
  }

  /// Met à jour la position d'un employé
  Future<void> updateEmployeePosition(int userId, double latitude, double longitude) async {
    final data = {
      'user_id': userId,
      'latitude': latitude,
      'longitude': longitude,
      'timestamp': DateTime.now().toIso8601String(),
    };

    final response = await _httpService.post(
      '${AppConstants.monitoringEndpoint}/update-position',
      data: data,
    );

    if (!response.success) {
      throw ApiException(message: response.displayMessage);
    }
  }

  /// Obtient l'historique des positions d'un employé
  Future<List<Map<String, dynamic>>> getEmployeePositionHistory(int userId, {DateTime? startDate, DateTime? endDate}) async {
    final queryParams = <String, String>{
      'user_id': userId.toString(),
    };

    if (startDate != null) {
      queryParams['start_date'] = startDate.toIso8601String();
    }
    if (endDate != null) {
      queryParams['end_date'] = endDate.toIso8601String();
    }

    final response = await _httpService.get(
      '${AppConstants.monitoringEndpoint}/position-history',
      queryParameters: queryParams,
    );

    if (!response.success) {
      throw ApiException(message: response.displayMessage);
    }

    return List<Map<String, dynamic>>.from(response.data['positions'] ?? []);
  }

  /// Obtient la dernière position connue d'un employé
  Future<Map<String, dynamic>?> getEmployeeLastKnownPosition(int userId) async {
    try {
      final response = await _httpService.get(
        '${AppConstants.monitoringEndpoint}/last-position',
        queryParameters: {'user_id': userId.toString()},
      );

      if (response.success && response.data != null) {
        return response.data['position'];
      }
      return null;
    } catch (e) {
      debugPrint('Error getting last known position: $e');
      return null;
    }
  }

  /// Envoie une alerte de surveillance
  Future<void> sendMonitoringAlert(int userId, String alertType, String message) async {
    final data = {
      'user_id': userId,
      'alert_type': alertType,
      'message': message,
      'timestamp': DateTime.now().toIso8601String(),
    };

    final response = await _httpService.post(
      '${AppConstants.monitoringEndpoint}/alert',
      data: data,
    );

    if (!response.success) {
      throw ApiException(message: response.displayMessage);
    }
  }



  // Reports endpoints
  Future<ReportResponse> generateEmployeeReport({
    required String startDate,
    required String endDate,
    bool includeStats = true,
  }) async {
    final data = {
      'start_date': startDate,
      'end_date': endDate,
      'include_stats': includeStats,
    };

    final response = await _httpService.post<ReportResponse>(
      '${AppConstants.reportsEndpoint}/employees',
      data: data,
      fromJson: (json) => ReportResponse.fromJson(json),
    );

    if (response.success && response.data != null) {
      return response.data!;
    } else {
      throw ApiException(message: response.displayMessage);
    }
  }

  Future<ReportResponse> generateEmployeeReportById(int userId, {
    required String startDate,
    required String endDate,
  }) async {
    final data = {
      'start_date': startDate,
      'end_date': endDate,
    };

    final response = await _httpService.post<ReportResponse>(
      '${AppConstants.reportsEndpoint}/employees/$userId',
      data: data,
      fromJson: (json) => ReportResponse.fromJson(json),
    );

    if (response.success && response.data != null) {
      return response.data!;
    } else {
      throw ApiException(message: response.displayMessage);
    }
  }

  Future<ReportResponse> generateSiteReport(int siteId, {
    required String startDate,
    required String endDate,
  }) async {
    final data = {
      'start_date': startDate,
      'end_date': endDate,
    };

    final response = await _httpService.post<ReportResponse>(
      '${AppConstants.reportsEndpoint}/sites/$siteId',
      data: data,
      fromJson: (json) => ReportResponse.fromJson(json),
    );

    if (response.success && response.data != null) {
      return response.data!;
    } else {
      throw ApiException(message: response.displayMessage);
    }
  }

  // Employee presence verification endpoints
  Future<PresenceVerificationResponse> verifyEmployeePresence({
    required int userId,
    required double latitude,
    required double longitude,
    bool sendAlert = false,
  }) async {
    final data = {
      'user_id': userId,
      'latitude': latitude,
      'longitude': longitude,
      'send_alert': sendAlert,
    };

    final response = await _httpService.post<PresenceVerificationResponse>(
      '${AppConstants.reportsEndpoint}/verify-presence',
      data: data,
      fromJson: (json) => PresenceVerificationResponse.fromJson(json),
    );

    if (response.success && response.data != null) {
      return response.data!;
    } else {
      throw ApiException(message: response.displayMessage);
    }
  }

  Future<AllEmployeesCheckResponse> checkAllEmployeesPresence() async {
    final response = await _httpService.post<AllEmployeesCheckResponse>(
      '${AppConstants.reportsEndpoint}/check-all-employees',
      fromJson: (json) => AllEmployeesCheckResponse.fromJson(json),
    );

    if (response.success && response.data != null) {
      return response.data!;
    } else {
      throw ApiException(message: response.displayMessage);
    }
  }

  Future<void> downloadReport(String filename) async {
    final response = await _httpService.get(
      '${AppConstants.reportsEndpoint}/download/$filename',
    );

    if (!response.success) {
      throw ApiException(message: response.displayMessage);
    }
  }
}
