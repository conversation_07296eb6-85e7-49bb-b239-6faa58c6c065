<?php

// Check users in the database
chdir('C:\wamp64\www\clockin');
require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "👥 Users in database:\n";
echo "====================\n";

$users = \App\Models\User::all(['id', 'name', 'email', 'role']);

foreach($users as $user) {
    echo $user->id . ': ' . $user->name . ' (' . $user->email . ') - ' . $user->role . "\n";
}

echo "\n🏁 Done!\n";
?>
