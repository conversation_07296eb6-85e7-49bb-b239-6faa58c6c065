import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geocoding/geocoding.dart';
import 'dart:async';
import '../../constants/app_colors.dart';

class SimplePlaceSearch extends StatefulWidget {
  final Function(LatLng location, String placeName, String address) onPlaceSelected;
  final String? initialQuery;

  const SimplePlaceSearch({
    super.key,
    required this.onPlaceSelected,
    this.initialQuery,
  });

  @override
  State<SimplePlaceSearch> createState() => _SimplePlaceSearchState();
}

class _SimplePlaceSearchState extends State<SimplePlaceSearch> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  List<SearchResult> _searchResults = [];
  bool _isSearching = false;
  bool _showResults = false;

  // Limites géographiques de l'Algérie
  static const double _algeriaMinLat = 18.9681;
  static const double _algeriaMaxLat = 37.2962;
  static const double _algeriaMinLng = -8.6676;
  static const double _algeriaMaxLng = 11.9795;

  // Lieux populaires en Algérie avec leurs coordonnées
  final Map<String, LatLng> _popularPlaces = {
    // Universités
    'جامعة الجزائر 1': LatLng(36.7167, 3.1833),
    'جامعة هواري بومدين للعلوم والتكنولوجيا': LatLng(36.7167, 3.1833),
    'جامعة وهران 1': LatLng(35.6333, -0.6167),
    'جامعة قسنطينة 1': LatLng(36.3667, 6.6167),
    
    // Aéroports
    'مطار هواري بومدين الدولي': LatLng(36.6911, 3.2156),
    'مطار وهران أحمد بن بلة': LatLng(35.6239, -0.6211),
    'مطار قسنطينة محمد بوضياف': LatLng(36.2761, 6.6206),
    'مطار عنابة رابح بيطاط': LatLng(36.8222, 7.8092),
    
    // Hôpitaux
    'مستشفى مصطفى باشا الجامعي': LatLng(36.7539, 3.0419),
    'مستشفى الجزائر الجامعي': LatLng(36.7539, 3.0419),
    'مستشفى وهران الجامعي': LatLng(35.6911, -0.6417),
    'مستشفى قسنطينة الجامعي': LatLng(36.3650, 6.6147),
    
    // Centres commerciaux
    'مركز أردس التجاري': LatLng(36.7167, 3.1833),
    'مركز باب الزوار التجاري': LatLng(36.7539, 3.0419),
    'مركز وهران التجاري': LatLng(35.6911, -0.6417),
    
    // Monuments et sites touristiques
    'مقام الشهيد': LatLng(36.7539, 3.0419),
    'قصبة الجزائر': LatLng(36.7833, 3.0667),
    'جامع كتشاوة': LatLng(36.7833, 3.0667),
    'حديقة التجارب الحامة': LatLng(36.7667, 3.0833),
    'قلعة سانتا كروز وهران': LatLng(35.7167, -0.6333),
    'جسر سيدي مسيد قسنطينة': LatLng(36.3667, 6.6167),
    
    // Gares et transports
    'محطة قطار الجزائر': LatLng(36.7833, 3.0667),
    'محطة قطار وهران': LatLng(35.6911, -0.6417),
    'محطة قطار قسنطينة': LatLng(36.3650, 6.6147),
    
    // Banques et administrations
    'بنك الجزائر المركزي': LatLng(36.7833, 3.0667),
    'وزارة الداخلية': LatLng(36.7833, 3.0667),
    'ولاية الجزائر': LatLng(36.7833, 3.0667),
    'ولاية وهران': LatLng(35.6911, -0.6417),
    'ولاية قسنطينة': LatLng(36.3650, 6.6147),
  };

  @override
  void initState() {
    super.initState();
    if (widget.initialQuery != null) {
      _searchController.text = widget.initialQuery!;
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSearchField(),
        if (_showResults && _searchResults.isNotEmpty) ...[
          const SizedBox(height: 8),
          _buildSearchResults(),
        ],
      ],
    );
  }

  Widget _buildSearchField() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.backgroundWhite,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.borderLight),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        controller: _searchController,
        focusNode: _focusNode,
        decoration: InputDecoration(
          hintText: 'ابحث عن أي مكان في الجزائر (مثال: جامعة الجزائر، مطار هواري بومدين...)',
          hintStyle: TextStyle(
            color: AppColors.textSecondary,
            fontSize: 14,
          ),
          prefixIcon: Icon(
            Icons.search,
            color: AppColors.primaryBlue,
          ),
          suffixIcon: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (_isSearching)
                const Padding(
                  padding: EdgeInsets.all(12),
                  child: SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                ),
              if (_searchController.text.isNotEmpty)
                IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: _clearSearch,
                  color: AppColors.textSecondary,
                ),
            ],
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 16,
          ),
        ),
        onChanged: _onSearchChanged,
        onSubmitted: _performSearch,
        textInputAction: TextInputAction.search,
      ),
    );
  }

  Widget _buildSearchResults() {
    return Container(
      constraints: const BoxConstraints(maxHeight: 300),
      decoration: BoxDecoration(
        color: AppColors.backgroundWhite,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.borderLight),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListView.separated(
        shrinkWrap: true,
        itemCount: _searchResults.length,
        separatorBuilder: (context, index) => Divider(
          height: 1,
          color: AppColors.borderLight,
        ),
        itemBuilder: (context, index) {
          final result = _searchResults[index];
          return ListTile(
            leading: CircleAvatar(
              backgroundColor: AppColors.primaryBlue.withValues(alpha: 0.1),
              child: Icon(
                result.icon,
                color: AppColors.primaryBlue,
                size: 20,
              ),
            ),
            title: Text(
              result.name,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            subtitle: Text(
              result.address,
              style: TextStyle(
                color: AppColors.textSecondary,
                fontSize: 12,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            trailing: Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: AppColors.textSecondary,
            ),
            onTap: () => _selectPlace(result),
          );
        },
      ),
    );
  }

  void _onSearchChanged(String query) {
    if (query.length >= 2) {
      _debounceSearch(query);
    } else {
      setState(() {
        _searchResults.clear();
        _showResults = false;
      });
    }
  }

  Timer? _debounceTimer;
  void _debounceSearch(String query) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 300), () {
      _performSearch(query);
    });
  }

  Future<void> _performSearch(String query) async {
    if (query.trim().isEmpty) return;

    setState(() {
      _isSearching = true;
      _showResults = true;
    });

    try {
      final results = <SearchResult>[];
      
      // 1. Recherche dans les lieux populaires prédéfinis
      final popularMatches = _searchInPopularPlaces(query);
      results.addAll(popularMatches);
      
      // 2. Recherche par géocodage
      final geocodingResults = await _searchWithGeocoding(query);
      results.addAll(geocodingResults);

      setState(() {
        _searchResults = results.take(10).toList(); // Limiter à 10 résultats
        _isSearching = false;
      });
    } catch (e) {
      debugPrint('Erreur de recherche: $e');
      setState(() {
        _searchResults.clear();
        _isSearching = false;
      });
    }
  }

  List<SearchResult> _searchInPopularPlaces(String query) {
    final results = <SearchResult>[];
    final lowerQuery = query.toLowerCase();
    
    for (final entry in _popularPlaces.entries) {
      if (entry.key.toLowerCase().contains(lowerQuery)) {
        results.add(SearchResult(
          name: entry.key,
          address: 'مكان مشهور في الجزائر',
          location: entry.value,
          icon: _getIconForPlace(entry.key),
        ));
      }
    }
    
    return results;
  }

  Future<List<SearchResult>> _searchWithGeocoding(String query) async {
    try {
      // Ajouter "Algeria" à la recherche pour limiter aux résultats algériens
      final searchQuery = '$query, Algeria';
      final locations = await locationFromAddress(searchQuery);
      
      final results = <SearchResult>[];
      
      for (final location in locations.take(5)) {
        final latLng = LatLng(location.latitude, location.longitude);
        
        // Vérifier si c'est en Algérie
        if (_isInAlgeria(latLng)) {
          try {
            final placemarks = await placemarkFromCoordinates(
              location.latitude,
              location.longitude,
            );
            
            if (placemarks.isNotEmpty) {
              final placemark = placemarks.first;
              final address = [
                placemark.street,
                placemark.locality,
                placemark.administrativeArea,
                'الجزائر',
              ].where((element) => element != null && element.isNotEmpty).join(', ');
              
              results.add(SearchResult(
                name: query,
                address: address,
                location: latLng,
                icon: Icons.place,
              ));
            }
          } catch (e) {
            debugPrint('Erreur de géocodage inverse: $e');
          }
        }
      }
      
      return results;
    } catch (e) {
      debugPrint('Erreur de géocodage: $e');
      return [];
    }
  }

  IconData _getIconForPlace(String placeName) {
    final name = placeName.toLowerCase();
    
    if (name.contains('جامعة') || name.contains('مدرسة')) {
      return Icons.school;
    } else if (name.contains('مستشفى') || name.contains('صيدلية')) {
      return Icons.local_hospital;
    } else if (name.contains('مطار')) {
      return Icons.flight;
    } else if (name.contains('بنك')) {
      return Icons.account_balance;
    } else if (name.contains('مركز') && name.contains('تجاري')) {
      return Icons.shopping_cart;
    } else if (name.contains('جامع') || name.contains('مسجد')) {
      return Icons.mosque;
    } else if (name.contains('ولاية') || name.contains('وزارة')) {
      return Icons.account_balance;
    } else if (name.contains('محطة') || name.contains('قطار')) {
      return Icons.train;
    } else if (name.contains('حديقة') || name.contains('منتزه')) {
      return Icons.park;
    } else {
      return Icons.place;
    }
  }

  bool _isInAlgeria(LatLng location) {
    return location.latitude >= _algeriaMinLat &&
           location.latitude <= _algeriaMaxLat &&
           location.longitude >= _algeriaMinLng &&
           location.longitude <= _algeriaMaxLng;
  }

  void _selectPlace(SearchResult result) {
    _searchController.text = result.name;
    setState(() {
      _showResults = false;
    });
    _focusNode.unfocus();
    
    widget.onPlaceSelected(
      result.location,
      result.name,
      result.address,
    );
  }

  void _clearSearch() {
    _searchController.clear();
    setState(() {
      _searchResults.clear();
      _showResults = false;
    });
  }
}

class SearchResult {
  final String name;
  final String address;
  final LatLng location;
  final IconData icon;

  SearchResult({
    required this.name,
    required this.address,
    required this.location,
    required this.icon,
  });
}
