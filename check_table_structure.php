<?php

// Script pour vérifier la structure des tables
require_once 'C:/wamp64/www/clockin/vendor/autoload.php';
$app = require_once 'C:/wamp64/www/clockin/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;

echo "=== STRUCTURE DES TABLES CLOCKIN ===\n\n";

try {
    // 1. Structure de la table users
    echo "1. STRUCTURE TABLE 'users':\n";
    echo str_repeat("-", 50) . "\n";
    
    $userColumns = DB::select("DESCRIBE users");
    foreach ($userColumns as $column) {
        echo sprintf(
            "Field: %s | Type: %s | Null: %s | Key: %s | Default: %s\n",
            $column->Field,
            $column->Type,
            $column->Null,
            $column->Key,
            $column->Default ?? 'NULL'
        );
    }
    
    // 2. Structure de la table sites
    echo "\n2. STRUCTURE TABLE 'sites':\n";
    echo str_repeat("-", 50) . "\n";
    
    $siteColumns = DB::select("DESCRIBE sites");
    foreach ($siteColumns as $column) {
        echo sprintf(
            "Field: %s | Type: %s | Null: %s | Key: %s | Default: %s\n",
            $column->Field,
            $column->Type,
            $column->Null,
            $column->Key,
            $column->Default ?? 'NULL'
        );
    }
    
    // 3. Vérifier si la table assignments existe
    echo "\n3. VÉRIFICATION TABLE 'assignments':\n";
    echo str_repeat("-", 50) . "\n";
    
    try {
        $assignmentColumns = DB::select("DESCRIBE assignments");
        foreach ($assignmentColumns as $column) {
            echo sprintf(
                "Field: %s | Type: %s | Null: %s | Key: %s | Default: %s\n",
                $column->Field,
                $column->Type,
                $column->Null,
                $column->Key,
                $column->Default ?? 'NULL'
            );
        }
    } catch (Exception $e) {
        echo "❌ Table 'assignments' n'existe pas: " . $e->getMessage() . "\n";
    }
    
    // 4. Lister toutes les tables
    echo "\n4. TOUTES LES TABLES:\n";
    echo str_repeat("-", 50) . "\n";
    
    $tables = DB::select("SHOW TABLES");
    $databaseName = DB::connection()->getDatabaseName();
    
    foreach ($tables as $table) {
        $tableName = $table->{"Tables_in_$databaseName"};
        echo "- $tableName\n";
    }
    
    // 5. Données actuelles des utilisateurs (sans default_site_id)
    echo "\n5. UTILISATEURS ACTUELS:\n";
    echo str_repeat("-", 50) . "\n";
    
    $users = DB::table('users')->select('id', 'name', 'email', 'role')->get();
    foreach ($users as $user) {
        echo sprintf(
            "ID: %d | Name: %s | Email: %s | Role: %s\n",
            $user->id,
            $user->name,
            $user->email,
            $user->role
        );
    }
    
    // 6. Sites disponibles
    echo "\n6. SITES DISPONIBLES:\n";
    echo str_repeat("-", 50) . "\n";
    
    $sites = DB::table('sites')->get();
    foreach ($sites as $site) {
        echo sprintf(
            "ID: %d | Name: %s | Lat: %s | Lon: %s\n",
            $site->id,
            $site->name,
            $site->latitude,
            $site->longitude
        );
    }
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "ANALYSE TERMINÉE\n";
echo str_repeat("=", 60) . "\n";
