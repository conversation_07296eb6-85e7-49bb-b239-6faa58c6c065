<?php

// Test Excel generation directly
echo "🔧 Testing Excel Generation Directly\n";
echo "====================================\n\n";

try {
    chdir('C:\wamp64\www\clockin');
    require_once 'vendor/autoload.php';
    
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "✅ Laravel bootstrapped\n";
    
    // Test the SimpleDetailedPointagesExport directly
    $userId = 5;
    $user = \App\Models\User::findOrFail($userId);
    $startDate = \Carbon\Carbon::parse('2025-07-28')->startOfDay();
    $endDate = \Carbon\Carbon::parse('2025-08-04')->endOfDay();
    
    $pointages = \App\Models\Pointage::with(['site:id,name'])
        ->where('user_id', $userId)
        ->whereBetween('debut_pointage', [$startDate, $endDate])
        ->orderBy('debut_pointage', 'desc')
        ->limit(50)
        ->get();
    
    echo "✅ Data retrieved: {$pointages->count()} pointages\n";
    
    $data = [
        'user' => $user,
        'period' => [
            'start' => $startDate,
            'end' => $endDate
        ],
        'pointages' => $pointages,
        'statistics' => [
            'total_pointages' => $pointages->count(),
            'completed_pointages' => $pointages->whereNotNull('fin_pointage')->count(),
            'total_hours' => $pointages->sum(function($p) {
                return $p->fin_pointage ? 
                    \Carbon\Carbon::parse($p->debut_pointage)->diffInHours(\Carbon\Carbon::parse($p->fin_pointage)) : 0;
            })
        ]
    ];
    
    echo "✅ Data prepared\n";
    
    // Test creating the export class
    $export = new \App\Exports\SimpleDetailedPointagesExport($data);
    echo "✅ Export class created\n";
    
    // Test array generation
    $array = $export->array();
    echo "✅ Array generated: " . count($array) . " rows\n";
    
    // Test Excel generation with minimal memory
    ini_set('memory_limit', '64M');
    ini_set('max_execution_time', 60);
    
    $filename = 'test_excel_' . time() . '.xlsx';
    
    echo "🔄 Attempting Excel generation...\n";
    
    \Maatwebsite\Excel\Facades\Excel::store($export, $filename, 'local');
    
    $filePath = storage_path('app/' . $filename);
    
    if (file_exists($filePath)) {
        $fileSize = filesize($filePath);
        echo "✅ Excel file generated successfully!\n";
        echo "   📁 File: $filename\n";
        echo "   📏 Size: $fileSize bytes\n";
        
        // Clean up
        unlink($filePath);
        echo "   🧹 Test file cleaned up\n";
    } else {
        echo "❌ Excel file was not created\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "📍 File: " . $e->getFile() . " Line: " . $e->getLine() . "\n";
    echo "📋 Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n🏁 Direct Excel test completed!\n";
?>
