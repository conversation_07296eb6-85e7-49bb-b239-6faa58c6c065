<?php

echo "=== TEST FINAL APRÈS CORRECTION ===\n";

// Connexion
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://192.168.0.50:8000/api/auth/login');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
    'email' => '<EMAIL>',
    'password' => 'password123'
]));
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$loginResponse = curl_exec($ch);
curl_close($ch);

$loginData = json_decode($loginResponse, true);
if (!isset($loginData['data']['token'])) {
    echo "❌ Échec de connexion\n";
    exit;
}

$token = $loginData['data']['token'];
echo "✅ Connexion réussie\n\n";

// Test avec timestamp exact
$clientTime = new DateTime();
$clientTimestamp = $clientTime->getTimestamp();

echo "📱 DONNÉES CLIENT:\n";
echo "Heure locale: " . $clientTime->format('H:i:s') . "\n";
echo "Timestamp: $clientTimestamp\n";
echo "Date complète: " . $clientTime->format('Y-m-d H:i:s') . "\n\n";

echo "📤 ENVOI REQUÊTE AVEC TIMESTAMP OBLIGATOIRE...\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://192.168.0.50:8000/api/pointage');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
    'latitude' => 36.1331744,
    'longitude' => 4.7352042,
    'timestamp' => $clientTimestamp, // OBLIGATOIRE
    'accuracy' => 10.0,
    'exact_time' => $clientTime->format('c')
]));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Authorization: Bearer ' . $token,
    'Content-Type: application/json'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "📥 RÉPONSE BACKEND:\n";
echo "Status HTTP: $httpCode\n";

if ($httpCode === 200 || $httpCode === 201) {
    $responseData = json_decode($response, true);
    
    // Déterminer quel champ utiliser
    $timeField = isset($responseData['data']['debut_pointage']) ? 'debut_pointage' : 'fin_pointage';
    $actionType = $timeField === 'debut_pointage' ? 'CHECK-IN' : 'CHECK-OUT';
    
    if (isset($responseData['data'][$timeField])) {
        $backendTimeStr = $responseData['data'][$timeField];
        $backendTime = new DateTime($backendTimeStr);
        $timeDiff = $backendTime->getTimestamp() - $clientTimestamp;
        
        echo "Action: $actionType\n";
        echo "Backend Time: " . $backendTime->format('H:i:s') . "\n";
        echo "Backend Date: " . $backendTime->format('Y-m-d H:i:s') . "\n";
        echo "Différence: $timeDiff secondes\n\n";
        
        echo str_repeat("=", 50) . "\n";
        echo "🎯 RÉSULTAT FINAL\n";
        echo str_repeat("=", 50) . "\n";
        
        if ($timeDiff == 0) {
            echo "🎉 SUCCÈS TOTAL !\n";
            echo "✅ PARFAIT - Heure exacte préservée\n";
            echo "✅ Client Time = Backend Time\n";
            echo "✅ Différence = 0 secondes\n";
            echo "✅ Synchronisation parfaite\n";
            echo "✅ Problème DÉFINITIVEMENT résolu\n";
            echo "✅ Système prêt pour production\n\n";
            
            echo "🏆 OBJECTIFS ATTEINTS:\n";
            echo "✅ Heure affichée = Heure enregistrée\n";
            echo "✅ Précision absolue\n";
            echo "✅ Cohérence totale\n";
            echo "✅ Fiabilité garantie\n";
            
        } elseif (abs($timeDiff) <= 2) {
            echo "✅ EXCELLENT - Précision acceptable\n";
            echo "Différence minime: $timeDiff secondes\n";
            echo "Système fonctionnel\n";
            
        } else {
            echo "❌ PROBLÈME PERSISTANT\n";
            echo "Différence: $timeDiff secondes\n";
            echo "🔧 VÉRIFICATIONS REQUISES:\n";
            echo "1. PointageController.php modifié ?\n";
            echo "2. Utilise Carbon::createFromTimestamp() ?\n";
            echo "3. Validation timestamp 'required' ?\n";
            echo "4. Cache Laravel vidé ?\n";
        }
        
    } else {
        echo "❌ Pas de données de temps dans la réponse\n";
        echo "Réponse complète:\n";
        echo json_encode($responseData, JSON_PRETTY_PRINT) . "\n";
    }
} else {
    echo "❌ Erreur HTTP: $response\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "📊 DIAGNOSTIC COMPLET TERMINÉ\n";
echo str_repeat("=", 60) . "\n";

unlink(__FILE__);
?>
