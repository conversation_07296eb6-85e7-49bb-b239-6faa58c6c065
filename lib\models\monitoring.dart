import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';
import 'user.dart';
import 'site.dart';

part 'monitoring.g.dart';

@JsonSerializable()
class EmployeeMonitoringResponse extends Equatable {
  final Employee employee;
  @Json<PERSON><PERSON>(name: 'assigned_site')
  final AssignedSite assignedSite;
  @Json<PERSON>ey(name: 'current_position')
  final CurrentPosition currentPosition;
  @<PERSON>sonKey(name: 'is_on_assigned_site')
  final bool isOnAssignedSite;
  @<PERSON>son<PERSON>ey(name: 'distance_from_site')
  final double distanceFromSite;
  @<PERSON>son<PERSON>ey(name: 'max_allowed_distance')
  final double maxAllowedDistance;
  @Json<PERSON><PERSON>(name: 'presence_status')
  final String presenceStatus;
  @Json<PERSON><PERSON>(name: 'check_time')
  final DateTime checkTime;
  final String message;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'message_ar')
  final String messageAr;

  const EmployeeMonitoringResponse({
    required this.employee,
    required this.assignedSite,
    required this.currentPosition,
    required this.isOnAssignedSite,
    required this.distanceFromSite,
    required this.maxAllowedDistance,
    required this.presenceStatus,
    required this.checkTime,
    required this.message,
    required this.messageAr,
  });

  factory EmployeeMonitoringResponse.fromJson(Map<String, dynamic> json) =>
      _$EmployeeMonitoringResponseFromJson(json);

  Map<String, dynamic> toJson() => _$EmployeeMonitoringResponseToJson(this);

  @override
  List<Object?> get props => [
    employee,
    assignedSite,
    currentPosition,
    isOnAssignedSite,
    distanceFromSite,
    maxAllowedDistance,
    presenceStatus,
    checkTime,
    message,
    messageAr,
  ];
}

@JsonSerializable()
class Employee extends Equatable {
  final int id;
  final String name;
  final String email;
  final String role;

  const Employee({
    required this.id,
    required this.name,
    required this.email,
    required this.role,
  });

  factory Employee.fromJson(Map<String, dynamic> json) =>
      _$EmployeeFromJson(json);

  Map<String, dynamic> toJson() => _$EmployeeToJson(this);

  @override
  List<Object?> get props => [id, name, email, role];
}

@JsonSerializable()
class AssignedSite extends Equatable {
  final int id;
  final String name;
  final double latitude;
  final double longitude;

  const AssignedSite({
    required this.id,
    required this.name,
    required this.latitude,
    required this.longitude,
  });

  factory AssignedSite.fromJson(Map<String, dynamic> json) =>
      _$AssignedSiteFromJson(json);

  Map<String, dynamic> toJson() => _$AssignedSiteToJson(this);

  @override
  List<Object?> get props => [id, name, latitude, longitude];
}

@JsonSerializable()
class CurrentPosition extends Equatable {
  final double latitude;
  final double longitude;

  const CurrentPosition({required this.latitude, required this.longitude});

  factory CurrentPosition.fromJson(Map<String, dynamic> json) =>
      _$CurrentPositionFromJson(json);

  Map<String, dynamic> toJson() => _$CurrentPositionToJson(this);

  @override
  List<Object?> get props => [latitude, longitude];
}

@JsonSerializable()
class AllEmployeesMonitoringResponse extends Equatable {
  final MonitoringSummary summary;
  @JsonKey(name: 'detailed_results')
  final List<DetailedResult> detailedResults;
  @JsonKey(name: 'check_time')
  final DateTime checkTime;

  const AllEmployeesMonitoringResponse({
    required this.summary,
    required this.detailedResults,
    required this.checkTime,
  });

  factory AllEmployeesMonitoringResponse.fromJson(Map<String, dynamic> json) =>
      _$AllEmployeesMonitoringResponseFromJson(json);

  Map<String, dynamic> toJson() => _$AllEmployeesMonitoringResponseToJson(this);

  @override
  List<Object?> get props => [summary, detailedResults, checkTime];
}

@JsonSerializable()
class MonitoringSummary extends Equatable {
  @JsonKey(name: 'total_checked')
  final int totalChecked;
  @JsonKey(name: 'present_on_site')
  final int presentOnSite;
  @JsonKey(name: 'absent_from_site')
  final int absentFromSite;
  @JsonKey(name: 'no_assigned_site')
  final int noAssignedSite;
  @JsonKey(name: 'notifications_sent')
  final int notificationsSent;

  const MonitoringSummary({
    required this.totalChecked,
    required this.presentOnSite,
    required this.absentFromSite,
    required this.noAssignedSite,
    required this.notificationsSent,
  });

  factory MonitoringSummary.fromJson(Map<String, dynamic> json) =>
      _$MonitoringSummaryFromJson(json);

  Map<String, dynamic> toJson() => _$MonitoringSummaryToJson(this);

  @override
  List<Object?> get props => [
    totalChecked,
    presentOnSite,
    absentFromSite,
    noAssignedSite,
    notificationsSent,
  ];
}

@JsonSerializable()
class DetailedResult extends Equatable {
  @JsonKey(name: 'pointage_id')
  final int pointageId;
  @JsonKey(name: 'started_at')
  final DateTime startedAt;
  @JsonKey(name: 'check_result')
  final CheckResult checkResult;

  const DetailedResult({
    required this.pointageId,
    required this.startedAt,
    required this.checkResult,
  });

  factory DetailedResult.fromJson(Map<String, dynamic> json) =>
      _$DetailedResultFromJson(json);

  Map<String, dynamic> toJson() => _$DetailedResultToJson(this);

  @override
  List<Object?> get props => [pointageId, startedAt, checkResult];
}

@JsonSerializable()
class CheckResult extends Equatable {
  final Employee employee;
  @JsonKey(name: 'is_on_assigned_site')
  final bool isOnAssignedSite;
  @JsonKey(name: 'distance_from_site')
  final double distanceFromSite;

  const CheckResult({
    required this.employee,
    required this.isOnAssignedSite,
    required this.distanceFromSite,
  });

  factory CheckResult.fromJson(Map<String, dynamic> json) =>
      _$CheckResultFromJson(json);

  Map<String, dynamic> toJson() => _$CheckResultToJson(this);

  @override
  List<Object?> get props => [employee, isOnAssignedSite, distanceFromSite];
}

@JsonSerializable()
class MonitoringControlResponse extends Equatable {
  final String message;
  @JsonKey(name: 'message_ar')
  final String? messageAr;
  @JsonKey(name: 'monitoring_config')
  final MonitoringConfig? monitoringConfig;
  @JsonKey(name: 'monitoring_summary')
  final MonitoringSummaryControl? monitoringSummary;

  const MonitoringControlResponse({
    required this.message,
    this.messageAr,
    this.monitoringConfig,
    this.monitoringSummary,
  });

  factory MonitoringControlResponse.fromJson(Map<String, dynamic> json) =>
      _$MonitoringControlResponseFromJson(json);

  Map<String, dynamic> toJson() => _$MonitoringControlResponseToJson(this);

  @override
  List<Object?> get props => [
    message,
    messageAr,
    monitoringConfig,
    monitoringSummary,
  ];
}

@JsonSerializable()
class MonitoringConfig extends Equatable {
  @JsonKey(name: 'user_id')
  final int userId;
  @JsonKey(name: 'interval_minutes')
  final int intervalMinutes;
  @JsonKey(name: 'started_at')
  final DateTime startedAt;
  @JsonKey(name: 'expires_at')
  final DateTime expiresAt;

  const MonitoringConfig({
    required this.userId,
    required this.intervalMinutes,
    required this.startedAt,
    required this.expiresAt,
  });

  factory MonitoringConfig.fromJson(Map<String, dynamic> json) =>
      _$MonitoringConfigFromJson(json);

  Map<String, dynamic> toJson() => _$MonitoringConfigToJson(this);

  @override
  List<Object?> get props => [userId, intervalMinutes, startedAt, expiresAt];
}

@JsonSerializable()
class MonitoringSummaryControl extends Equatable {
  @JsonKey(name: 'duration_minutes')
  final int durationMinutes;
  @JsonKey(name: 'stopped_at')
  final DateTime stoppedAt;

  const MonitoringSummaryControl({
    required this.durationMinutes,
    required this.stoppedAt,
  });

  factory MonitoringSummaryControl.fromJson(Map<String, dynamic> json) =>
      _$MonitoringSummaryControlFromJson(json);

  Map<String, dynamic> toJson() => _$MonitoringSummaryControlToJson(this);

  @override
  List<Object?> get props => [durationMinutes, stoppedAt];
}

@JsonSerializable()
class MonitoringStatusResponse extends Equatable {
  @JsonKey(name: 'active_monitoring')
  final List<ActiveMonitoring> activeMonitoring;
  @JsonKey(name: 'total_monitored')
  final int totalMonitored;

  const MonitoringStatusResponse({
    required this.activeMonitoring,
    required this.totalMonitored,
  });

  factory MonitoringStatusResponse.fromJson(Map<String, dynamic> json) =>
      _$MonitoringStatusResponseFromJson(json);

  Map<String, dynamic> toJson() => _$MonitoringStatusResponseToJson(this);

  @override
  List<Object?> get props => [activeMonitoring, totalMonitored];
}

@JsonSerializable()
class ActiveMonitoring extends Equatable {
  @JsonKey(name: 'user_id')
  final int userId;
  @JsonKey(name: 'user_name')
  final String userName;
  @JsonKey(name: 'started_at')
  final DateTime startedAt;
  @JsonKey(name: 'interval_minutes')
  final int intervalMinutes;
  @JsonKey(name: 'expires_at')
  final DateTime expiresAt;

  const ActiveMonitoring({
    required this.userId,
    required this.userName,
    required this.startedAt,
    required this.intervalMinutes,
    required this.expiresAt,
  });

  factory ActiveMonitoring.fromJson(Map<String, dynamic> json) =>
      _$ActiveMonitoringFromJson(json);

  Map<String, dynamic> toJson() => _$ActiveMonitoringToJson(this);

  @override
  List<Object?> get props => [
    userId,
    userName,
    startedAt,
    intervalMinutes,
    expiresAt,
  ];
}
