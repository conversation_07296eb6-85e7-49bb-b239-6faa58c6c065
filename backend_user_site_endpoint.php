<?php

/**
 * SOLUTION IMMÉDIATE POUR LE PROBLÈME DE SITE ASSIGNÉ
 * 
 * À ajouter dans le backend ClockIn à C:\wamp64\www\clockin
 * 
 * ÉTAPES D'IMPLÉMENTATION :
 * 
 * 1. Ajouter cette route dans routes/api.php :
 *    Route::get('/sites/user-assigned', [SiteController::class, 'getUserAssigned']);
 * 
 * 2. Ajouter cette méthode dans app/Http/Controllers/SiteController.php :
 */

/**
 * Récupère le site assigné à l'utilisateur connecté
 * 
 * @authenticated
 * 
 * @response 200 {
 *   "success": true,
 *   "data": {
 *     "id": 1,
 *     "name": "Site Casablanca",
 *     "latitude": "33.57310000",
 *     "longitude": "-7.58980000",
 *     "rayon": 50
 *   }
 * }
 * 
 * @response 404 {
 *   "success": false,
 *   "message": "Aucun site assigné trouvé.",
 *   "message_ar": "لم يتم العثور على موقع مخصص."
 * }
 */
public function getUserAssigned(Request $request)
{
    try {
        $user = $request->user();
        
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'Utilisateur non authentifié.',
                'message_ar' => 'المستخدم غير مصادق عليه.'
            ], 401);
        }

        \Log::info("Getting assigned site for user: {$user->id} ({$user->email})");

        // Méthode 1: Utiliser le default_site_id de l'utilisateur (méthode principale)
        if ($user->default_site_id) {
            $site = \App\Models\Site::find($user->default_site_id);
            if ($site) {
                \Log::info("Found site via default_site_id: {$site->name} (ID: {$site->id})");
                return response()->json([
                    'success' => true,
                    'data' => $site,
                    'message' => 'Site assigné trouvé',
                    'message_ar' => 'تم العثور على الموقع المخصص'
                ]);
            } else {
                \Log::warning("Site with ID {$user->default_site_id} not found for user {$user->id}");
            }
        } else {
            \Log::info("User {$user->id} has no default_site_id set");
        }

        // Méthode 2: Chercher dans les relations many-to-many (si configurées)
        try {
            $site = $user->sites()->first();
            if ($site) {
                \Log::info("Found site via user relationship: {$site->name} (ID: {$site->id})");
                return response()->json([
                    'success' => true,
                    'data' => $site,
                    'message' => 'Site assigné trouvé via relation',
                    'message_ar' => 'تم العثور على الموقع المخصص عبر العلاقة'
                ]);
            }
        } catch (\Exception $e) {
            \Log::info("No sites relationship configured for user {$user->id}");
        }

        // Méthode 3: Chercher dans une table d'assignation séparée (si elle existe)
        try {
            $assignment = \DB::table('site_assignments')
                ->where('user_id', $user->id)
                ->where('is_active', true)
                ->first();
                
            if ($assignment) {
                $site = \App\Models\Site::find($assignment->site_id);
                if ($site) {
                    \Log::info("Found site via assignment table: {$site->name} (ID: {$site->id})");
                    return response()->json([
                        'success' => true,
                        'data' => $site,
                        'message' => 'Site assigné trouvé via table d\'assignation',
                        'message_ar' => 'تم العثور على الموقع المخصص عبر جدول التخصيص'
                    ]);
                }
            }
        } catch (\Exception $e) {
            \Log::info("No site_assignments table found for user {$user->id}");
        }

        // Méthode 4: Chercher le dernier pointage pour récupérer le site
        try {
            $lastPointage = \App\Models\Pointage::where('user_id', $user->id)
                ->with('site')
                ->orderBy('created_at', 'desc')
                ->first();
                
            if ($lastPointage && $lastPointage->site) {
                \Log::info("Found site via last pointage: {$lastPointage->site->name} (ID: {$lastPointage->site->id})");
                return response()->json([
                    'success' => true,
                    'data' => $lastPointage->site,
                    'message' => 'Site assigné trouvé via dernier pointage',
                    'message_ar' => 'تم العثور على الموقع المخصص عبر آخر نقطة'
                ]);
            }
        } catch (\Exception $e) {
            \Log::info("No pointage history found for user {$user->id}");
        }

        // Aucun site assigné trouvé
        \Log::warning("No assigned site found for user {$user->id}");
        return response()->json([
            'success' => false,
            'message' => 'Aucun site assigné trouvé',
            'message_ar' => 'لم يتم العثور على موقع مخصص',
            'data' => null
        ], 404);

    } catch (\Exception $e) {
        \Log::error("Error getting user assigned site: " . $e->getMessage());
        return response()->json([
            'success' => false,
            'message' => 'Erreur lors de la récupération du site assigné',
            'message_ar' => 'خطأ في استرداد الموقع المخصص',
            'error' => $e->getMessage()
        ], 500);
    }
}

/**
 * ALTERNATIVE SIMPLE : Ajouter cette route directement dans routes/api.php
 */
/*
Route::middleware('auth:sanctum')->get('/sites/user-assigned', function (Request $request) {
    $user = $request->user();
    
    if (!$user) {
        return response()->json([
            'success' => false,
            'message_ar' => 'المستخدم غير مصادق عليه'
        ], 401);
    }
    
    // Utiliser le default_site_id
    if ($user->default_site_id) {
        $site = \App\Models\Site::find($user->default_site_id);
        if ($site) {
            return response()->json([
                'success' => true,
                'data' => $site
            ]);
        }
    }
    
    return response()->json([
        'success' => false,
        'message_ar' => 'لم يتم تخصيص موقع'
    ], 404);
});
*/

/**
 * INSTRUCTIONS D'IMPLÉMENTATION :
 * 
 * 1. Copier le code de la méthode getUserAssigned() dans SiteController.php
 * 2. Ajouter la route dans routes/api.php
 * 3. Vérifier que l'utilisateur a un default_site_id dans la base de données :
 *    UPDATE users SET default_site_id = 1 WHERE id = {employee_id};
 * 4. Tester avec Postman :
 *    GET http://192.168.0.50:8000/api/sites/user-assigned
 *    Headers: Authorization: Bearer {token}
 * 
 * VÉRIFICATIONS BASE DE DONNÉES :
 * 
 * -- Vérifier les assignations actuelles
 * SELECT u.id, u.name, u.email, u.default_site_id, s.name as site_name 
 * FROM users u 
 * LEFT JOIN sites s ON u.default_site_id = s.id 
 * WHERE u.role = 'employee';
 * 
 * -- Assigner un site à un employé
 * UPDATE users SET default_site_id = 1 WHERE id = {employee_id};
 */
