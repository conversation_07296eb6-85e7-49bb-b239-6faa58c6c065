<?php

// Script pour ajouter la méthode getUserAssigned au SiteController
echo "=== AJOUT DE LA MÉTHODE getUserAssigned ===\n";

$controllerPath = 'C:/wamp64/www/clockin/app/Http/Controllers/Site/SiteController.php';

// Lire le contenu actuel
$content = file_get_contents($controllerPath);

// Vérifier si la méthode existe déjà
if (strpos($content, 'getUserAssigned') !== false) {
    echo "✅ La méthode getUserAssigned existe déjà!\n";
    exit;
}

// Méthode à ajouter
$newMethod = '
    /**
     * Get User Assigned Site
     *
     * Récupère le site assigné à l\'utilisateur connecté via la table assignments
     *
     * @authenticated
     *
     * @response 200 {
     *   "success": true,
     *   "data": {
     *     "id": 5,
     *     "name": "ain soltan",
     *     "latitude": "36.13317760",
     *     "longitude": "4.73510890"
     *   },
     *   "message": "Site assigné trouvé",
     *   "message_ar": "تم العثور على الموقع المخصص"
     * }
     *
     * @response 404 {
     *   "success": false,
     *   "message": "Aucun site assigné trouvé",
     *   "message_ar": "لم يتم العثور على موقع مخصص"
     * }
     */
    public function getUserAssigned(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            
            if (!$user) {
                return response()->json([
                    \'success\' => false,
                    \'message\' => \'Utilisateur non authentifié.\',
                    \'message_ar\' => \'المستخدم غير مصادق عليه.\'
                ], 401);
            }

            \Log::info("Getting assigned site for user: {$user->id} ({$user->email})");

            // Récupérer le site via la table assignments
            $assignment = \DB::table(\'assignments\')
                ->join(\'sites\', \'assignments.site_id\', \'=\', \'sites.id\')
                ->where(\'assignments.user_id\', $user->id)
                ->select(
                    \'sites.id\',
                    \'sites.name\',
                    \'sites.latitude\',
                    \'sites.longitude\',
                    \'sites.created_at\',
                    \'sites.updated_at\'
                )
                ->first();

            if ($assignment) {
                \Log::info("Found site via assignments table: {$assignment->name} (ID: {$assignment->id})");
                
                $site = [
                    \'id\' => $assignment->id,
                    \'name\' => $assignment->name,
                    \'latitude\' => $assignment->latitude,
                    \'longitude\' => $assignment->longitude,
                    \'created_at\' => $assignment->created_at,
                    \'updated_at\' => $assignment->updated_at
                ];
                
                return response()->json([
                    \'success\' => true,
                    \'data\' => $site,
                    \'message\' => \'Site assigné trouvé\',
                    \'message_ar\' => \'تم العثور على الموقع المخصص\'
                ]);
            }

            // Fallback: Chercher dans le dernier pointage
            try {
                $lastPointage = \DB::table(\'pointages\')
                    ->join(\'sites\', \'pointages.site_id\', \'=\', \'sites.id\')
                    ->where(\'pointages.user_id\', $user->id)
                    ->select(
                        \'sites.id\',
                        \'sites.name\',
                        \'sites.latitude\',
                        \'sites.longitude\',
                        \'sites.created_at\',
                        \'sites.updated_at\'
                    )
                    ->orderBy(\'pointages.created_at\', \'desc\')
                    ->first();
                    
                if ($lastPointage) {
                    \Log::info("Found site via last pointage: {$lastPointage->name} (ID: {$lastPointage->id})");
                    
                    $site = [
                        \'id\' => $lastPointage->id,
                        \'name\' => $lastPointage->name,
                        \'latitude\' => $lastPointage->latitude,
                        \'longitude\' => $lastPointage->longitude,
                        \'created_at\' => $lastPointage->created_at,
                        \'updated_at\' => $lastPointage->updated_at
                    ];
                    
                    return response()->json([
                        \'success\' => true,
                        \'data\' => $site,
                        \'message\' => \'Site assigné trouvé via dernier pointage\',
                        \'message_ar\' => \'تم العثور على الموقع المخصص عبر آخر نقطة\'
                    ]);
                }
            } catch (\Exception $e) {
                \Log::info("No pointage history found for user {$user->id}: " . $e->getMessage());
            }

            // Aucun site assigné trouvé
            \Log::warning("No assigned site found for user {$user->id}");
            return response()->json([
                \'success\' => false,
                \'message\' => \'Aucun site assigné trouvé\',
                \'message_ar\' => \'لم يتم العثور على موقع مخصص\',
                \'data\' => null
            ], 404);

        } catch (\Exception $e) {
            \Log::error("Error getting user assigned site: " . $e->getMessage());
            return response()->json([
                \'success\' => false,
                \'message\' => \'Erreur lors de la récupération du site assigné\',
                \'message_ar\' => \'خطأ في استرداد الموقع المخصص\',
                \'error\' => $e->getMessage()
            ], 500);
        }
    }
';

// Trouver la position de la dernière accolade fermante de la classe
$lastClosingBrace = strrpos($content, '}');

if ($lastClosingBrace !== false) {
    // Insérer la nouvelle méthode avant la dernière accolade
    $newContent = substr($content, 0, $lastClosingBrace) . $newMethod . "\n" . substr($content, $lastClosingBrace);
    
    // Sauvegarder le fichier modifié
    if (file_put_contents($controllerPath, $newContent)) {
        echo "✅ Méthode getUserAssigned ajoutée avec succès au SiteController!\n";
        
        // Maintenant modifier le fichier routes/api.php pour utiliser cette méthode
        echo "\n2. MODIFICATION DU FICHIER ROUTES:\n";
        echo str_repeat("-", 40) . "\n";
        
        $apiFilePath = 'C:/wamp64/www/clockin/routes/api.php';
        $apiContent = file_get_contents($apiFilePath);
        
        // Remplacer la route closure par un appel au controller
        $oldRoute = 'Route::get(\'/sites/user-assigned\', function (Request $request) {';
        $newRoute = 'Route::get(\'/sites/user-assigned\', [SiteController::class, \'getUserAssigned\']);';
        
        if (strpos($apiContent, $oldRoute) !== false) {
            // Trouver et remplacer toute la closure
            $pattern = '/Route::get\(\'\/sites\/user-assigned\', function \(Request \$request\) \{.*?\}\);/s';
            $newApiContent = preg_replace($pattern, $newRoute, $apiContent);
            
            if (file_put_contents($apiFilePath, $newApiContent)) {
                echo "✅ Route mise à jour pour utiliser le controller!\n";
            } else {
                echo "❌ Erreur lors de la mise à jour de la route\n";
            }
        } else {
            echo "❌ Route closure non trouvée dans api.php\n";
        }
        
        echo "\n📋 DÉTAILS:\n";
        echo "Méthode: SiteController::getUserAssigned\n";
        echo "URL: GET /api/sites/user-assigned\n";
        echo "Authentification: Token requis\n";
        echo "Accessible par: Tous les utilisateurs authentifiés\n";
        
    } else {
        echo "❌ Erreur lors de la sauvegarde du fichier!\n";
    }
} else {
    echo "❌ Impossible de trouver la structure de la classe SiteController!\n";
}

echo "\n" . str_repeat("=", 50) . "\n";
