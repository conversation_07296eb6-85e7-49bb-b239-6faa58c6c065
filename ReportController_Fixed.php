<?php

namespace App\Http\Controllers\Report;

use App\Http\Controllers\Controller;
use App\Http\Traits\ApiResponseTrait;
use App\Services\ExportService;
use App\Services\NotificationService;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

/**
 * @group Reports & Exports
 *
 * APIs pour la génération de rapports et exports Excel
 */
class ReportController extends Controller
{
    use ApiResponseTrait;

    private ExportService $exportService;
    private NotificationService $notificationService;

    public function __construct(
        ExportService $exportService,
        NotificationService $notificationService
    ) {
        $this->exportService = $exportService;
        $this->notificationService = $notificationService;
    }

    /**
     * Générer un rapport Excel global des employés
     */
    public function generateEmployeeReport(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'start_date' => 'required|date|before_or_equal:end_date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'include_stats' => 'boolean'
        ]);

        if ($validator->fails()) {
            return $this->errorResponse(
                'Données de validation invalides.',
                'بيانات التحقق غير صالحة.',
                422,
                null,
                $validator->errors()
            );
        }

        try {
            Log::info('Starting employee report generation', [
                'start_date' => $request->start_date,
                'end_date' => $request->end_date,
                'include_stats' => $request->boolean('include_stats', true)
            ]);

            $startDate = Carbon::parse($request->start_date)->startOfDay();
            $endDate = Carbon::parse($request->end_date)->endOfDay();

            $options = [
                'include_stats' => $request->boolean('include_stats', true)
            ];

            // Increase memory limit and execution time for report generation
            ini_set('memory_limit', '512M');
            ini_set('max_execution_time', 300); // 5 minutes

            $filePath = $this->exportService->generateEmployeeReport($startDate, $endDate, $options);
            
            if (!file_exists($filePath)) {
                throw new \Exception('Report file was not created');
            }

            $filename = basename($filePath);
            $fileSize = $this->formatFileSize(filesize($filePath));

            Log::info('Employee report generated successfully', [
                'filename' => $filename,
                'file_size' => $fileSize
            ]);

            return $this->successResponse([
                'filename' => $filename,
                'download_url' => "/api/reports/download/{$filename}",
                'file_size' => $fileSize,
                'generated_at' => now()->toISOString(),
                'period' => [
                    'start' => $startDate->toDateString(),
                    'end' => $endDate->toDateString()
                ]
            ], 'Rapport généré avec succès.', 'تم إنشاء التقرير بنجاح.');

        } catch (\Exception $e) {
            Log::error('Error generating employee report', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->errorResponse(
                'Erreur lors de la génération du rapport: ' . $e->getMessage(),
                'خطأ أثناء إنشاء التقرير: ' . $e->getMessage(),
                500,
                $e
            );
        }
    }

    /**
     * Générer un rapport Excel pour un employé spécifique
     */
    public function generateIndividualReport(Request $request, int $userId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'start_date' => 'required|date|before_or_equal:end_date',
            'end_date' => 'required|date|after_or_equal:start_date'
        ]);

        if ($validator->fails()) {
            return $this->errorResponse(
                'Données de validation invalides.',
                'بيانات التحقق غير صالحة.',
                422,
                null,
                $validator->errors()
            );
        }

        try {
            Log::info('Starting individual report generation', [
                'user_id' => $userId,
                'start_date' => $request->start_date,
                'end_date' => $request->end_date
            ]);

            // Check if user exists
            $user = \App\Models\User::find($userId);
            if (!$user) {
                return $this->errorResponse(
                    'Utilisateur non trouvé.',
                    'المستخدم غير موجود.',
                    404
                );
            }

            $startDate = Carbon::parse($request->start_date)->startOfDay();
            $endDate = Carbon::parse($request->end_date)->endOfDay();

            // Increase memory limit and execution time for report generation
            ini_set('memory_limit', '512M');
            ini_set('max_execution_time', 300); // 5 minutes

            $filePath = $this->exportService->generateIndividualEmployeeReport($userId, $startDate, $endDate);
            
            if (!file_exists($filePath)) {
                throw new \Exception('Report file was not created');
            }

            $filename = basename($filePath);
            $fileSize = $this->formatFileSize(filesize($filePath));

            Log::info('Individual report generated successfully', [
                'user_id' => $userId,
                'filename' => $filename,
                'file_size' => $fileSize
            ]);

            return $this->successResponse([
                'filename' => $filename,
                'download_url' => "/api/reports/download/{$filename}",
                'file_size' => $fileSize,
                'generated_at' => now()->toISOString(),
                'user_id' => $userId
            ], 'Rapport individuel généré avec succès.', 'تم إنشاء التقرير الفردي بنجاح.');

        } catch (\Exception $e) {
            Log::error('Error generating individual report', [
                'user_id' => $userId,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->errorResponse(
                'Erreur lors de la génération du rapport: ' . $e->getMessage(),
                'خطأ أثناء إنشاء التقرير: ' . $e->getMessage(),
                500,
                $e
            );
        }
    }

    /**
     * Générer un rapport Excel par site
     */
    public function generateSiteReport(Request $request, int $siteId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'start_date' => 'required|date|before_or_equal:end_date',
            'end_date' => 'required|date|after_or_equal:start_date'
        ]);

        if ($validator->fails()) {
            return $this->errorResponse(
                'Données de validation invalides.',
                'بيانات التحقق غير صالحة.',
                422,
                null,
                $validator->errors()
            );
        }

        try {
            Log::info('Starting site report generation', [
                'site_id' => $siteId,
                'start_date' => $request->start_date,
                'end_date' => $request->end_date
            ]);

            // Check if site exists
            $site = \App\Models\Site::find($siteId);
            if (!$site) {
                return $this->errorResponse(
                    'Site non trouvé.',
                    'الموقع غير موجود.',
                    404
                );
            }

            $startDate = Carbon::parse($request->start_date)->startOfDay();
            $endDate = Carbon::parse($request->end_date)->endOfDay();

            // Increase memory limit and execution time for report generation
            ini_set('memory_limit', '512M');
            ini_set('max_execution_time', 300); // 5 minutes

            $filePath = $this->exportService->generateSiteReport($siteId, $startDate, $endDate);
            
            if (!file_exists($filePath)) {
                throw new \Exception('Report file was not created');
            }

            $filename = basename($filePath);
            $fileSize = $this->formatFileSize(filesize($filePath));

            Log::info('Site report generated successfully', [
                'site_id' => $siteId,
                'filename' => $filename,
                'file_size' => $fileSize
            ]);

            return $this->successResponse([
                'filename' => $filename,
                'download_url' => "/api/reports/download/{$filename}",
                'file_size' => $fileSize,
                'generated_at' => now()->toISOString(),
                'site_id' => $siteId
            ], 'Rapport de site généré avec succès.', 'تم إنشاء تقرير الموقع بنجاح.');

        } catch (\Exception $e) {
            Log::error('Error generating site report', [
                'site_id' => $siteId,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->errorResponse(
                'Erreur lors de la génération du rapport: ' . $e->getMessage(),
                'خطأ أثناء إنشاء التقرير: ' . $e->getMessage(),
                500,
                $e
            );
        }
    }

    /**
     * Télécharger un fichier de rapport
     */
    public function downloadReport(string $filename): BinaryFileResponse
    {
        $filePath = storage_path('app/' . $filename);

        if (!file_exists($filePath)) {
            abort(404, 'Fichier non trouvé');
        }

        // Vérifier que le fichier appartient bien aux rapports (sécurité)
        if (!str_contains($filename, 'rapport_')) {
            abort(403, 'Accès non autorisé');
        }

        return response()->download($filePath, $filename, [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        ]);
    }

    /**
     * Vérifier la présence d'un employé sur site
     */
    public function verifyEmployeePresence(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|integer|exists:users,id',
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
            'send_alert' => 'boolean'
        ]);

        if ($validator->fails()) {
            return $this->errorResponse(
                'Données de validation invalides.',
                'بيانات التحقق غير صالحة.',
                422,
                null,
                $validator->errors()
            );
        }

        try {
            $user = \App\Models\User::findOrFail($request->user_id);

            $verificationResult = $this->notificationService->verifyEmployeePresence(
                $user,
                $request->latitude,
                $request->longitude
            );

            // Envoyer une alerte si demandé et si l'employé n'est pas présent
            if ($request->boolean('send_alert', false) && !$verificationResult['is_present']) {
                $this->notificationService->sendPresenceAlert($user, $verificationResult);
            }

            return $this->successResponse(
                $verificationResult,
                'Vérification de présence effectuée.',
                'تم التحقق من الحضور.'
            );

        } catch (\Exception $e) {
            Log::error('Error verifying employee presence', [
                'user_id' => $request->user_id,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);

            return $this->errorResponse(
                'Erreur lors de la vérification: ' . $e->getMessage(),
                'خطأ أثناء التحقق: ' . $e->getMessage(),
                500,
                $e
            );
        }
    }

    /**
     * Vérifier tous les employés actifs
     */
    public function checkAllActiveEmployees(): JsonResponse
    {
        try {
            $results = $this->notificationService->checkAllActiveEmployees();

            $summary = [
                'total_checked' => count($results),
                'present_count' => count(array_filter($results, fn($r) => $r['verification']['is_present'])),
                'absent_count' => count(array_filter($results, fn($r) => !$r['verification']['is_present'])),
                'verifications' => $results
            ];

            return $this->successResponse(
                $summary,
                'Vérification globale effectuée.',
                'تم التحقق الشامل.'
            );

        } catch (\Exception $e) {
            Log::error('Error checking all active employees', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);

            return $this->errorResponse(
                'Erreur lors de la vérification globale: ' . $e->getMessage(),
                'خطأ أثناء التحقق الشامل: ' . $e->getMessage(),
                500,
                $e
            );
        }
    }

    /**
     * Formate la taille d'un fichier
     */
    private function formatFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

        return round($bytes, 2) . ' ' . $units[$pow];
    }
}
