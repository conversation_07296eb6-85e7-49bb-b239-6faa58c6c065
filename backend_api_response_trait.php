<?php

namespace App\Http\Traits;

use Illuminate\Http\JsonResponse;

/**
 * Trait pour standardiser les réponses API
 */
trait ApiResponseTrait
{
    /**
     * Réponse de succès standardisée
     *
     * @param mixed $data
     * @param string $message
     * @param string $messageAr
     * @param int $statusCode
     * @param array $meta
     * @return JsonResponse
     */
    protected function successResponse(
        $data = null,
        string $message = 'Opération réussie',
        string $messageAr = 'تمت العملية بنجاح',
        int $statusCode = 200,
        array $meta = []
    ): JsonResponse {
        $response = [
            'success' => true,
            'message' => $message,
            'message_ar' => $messageAr,
            'data' => $data,
            'timestamp' => now()->toISOString(),
        ];

        if (!empty($meta)) {
            $response['meta'] = $meta;
        }

        return response()->json($response, $statusCode);
    }

    /**
     * Réponse d'erreur standardisée
     *
     * @param string $message
     * @param string $messageAr
     * @param int $statusCode
     * @param mixed $errors
     * @param string|null $code
     * @return JsonResponse
     */
    protected function errorResponse(
        string $message = 'Une erreur est survenue',
        string $messageAr = 'حدث خطأ',
        int $statusCode = 400,
        $errors = null,
        string $code = null
    ): JsonResponse {
        $response = [
            'success' => false,
            'message' => $message,
            'message_ar' => $messageAr,
            'timestamp' => now()->toISOString(),
        ];

        if ($errors !== null) {
            $response['errors'] = $errors;
        }

        if ($code !== null) {
            $response['code'] = $code;
        }

        return response()->json($response, $statusCode);
    }

    /**
     * Réponse de validation d'erreur
     *
     * @param mixed $errors
     * @param string $message
     * @param string $messageAr
     * @return JsonResponse
     */
    protected function validationErrorResponse(
        $errors,
        string $message = 'Erreurs de validation',
        string $messageAr = 'أخطاء التحقق'
    ): JsonResponse {
        return $this->errorResponse($message, $messageAr, 422, $errors, 'VALIDATION_ERROR');
    }

    /**
     * Réponse non autorisée
     *
     * @param string $message
     * @param string $messageAr
     * @return JsonResponse
     */
    protected function unauthorizedResponse(
        string $message = 'Non autorisé',
        string $messageAr = 'غير مخول'
    ): JsonResponse {
        return $this->errorResponse($message, $messageAr, 401, null, 'UNAUTHORIZED');
    }

    /**
     * Réponse interdite
     *
     * @param string $message
     * @param string $messageAr
     * @return JsonResponse
     */
    protected function forbiddenResponse(
        string $message = 'Accès interdit',
        string $messageAr = 'الوصول محظور'
    ): JsonResponse {
        return $this->errorResponse($message, $messageAr, 403, null, 'FORBIDDEN');
    }

    /**
     * Réponse non trouvé
     *
     * @param string $message
     * @param string $messageAr
     * @return JsonResponse
     */
    protected function notFoundResponse(
        string $message = 'Ressource non trouvée',
        string $messageAr = 'المورد غير موجود'
    ): JsonResponse {
        return $this->errorResponse($message, $messageAr, 404, null, 'NOT_FOUND');
    }

    /**
     * Réponse de conflit
     *
     * @param string $message
     * @param string $messageAr
     * @return JsonResponse
     */
    protected function conflictResponse(
        string $message = 'Conflit de données',
        string $messageAr = 'تضارب في البيانات'
    ): JsonResponse {
        return $this->errorResponse($message, $messageAr, 409, null, 'CONFLICT');
    }

    /**
     * Réponse d'erreur serveur
     *
     * @param string $message
     * @param string $messageAr
     * @param mixed $errors
     * @return JsonResponse
     */
    protected function serverErrorResponse(
        string $message = 'Erreur interne du serveur',
        string $messageAr = 'خطأ داخلي في الخادم',
        $errors = null
    ): JsonResponse {
        return $this->errorResponse($message, $messageAr, 500, $errors, 'SERVER_ERROR');
    }

    /**
     * Réponse paginée
     *
     * @param mixed $data
     * @param array $pagination
     * @param string $message
     * @param string $messageAr
     * @return JsonResponse
     */
    protected function paginatedResponse(
        $data,
        array $pagination,
        string $message = 'Données récupérées avec succès',
        string $messageAr = 'تم استرداد البيانات بنجاح'
    ): JsonResponse {
        return $this->successResponse($data, $message, $messageAr, 200, [
            'pagination' => $pagination
        ]);
    }
}

/*
|--------------------------------------------------------------------------
| Instructions d'installation
|--------------------------------------------------------------------------
|
| Copiez ce fichier dans app/Http/Traits/ApiResponseTrait.php
| dans votre projet Laravel backend
|
*/
