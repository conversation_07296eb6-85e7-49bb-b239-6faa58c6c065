import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../providers/auth_provider.dart';
import '../../routes/app_routes.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  bool _notificationsEnabled = true;
  bool _locationTrackingEnabled = true;
  bool _autoBackupEnabled = false;
  String _selectedLanguage = 'ar';
  String _selectedTheme = 'light';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الإعدادات'),
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: AppColors.textWhite,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildGeneralSettings(),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildNotificationSettings(),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildLocationSettings(),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildDataSettings(),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildSecuritySettings(),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildAboutSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildGeneralSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الإعدادات العامة',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.primaryBlue,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            ListTile(
              leading: const Icon(Icons.language, color: AppColors.primaryBlue),
              title: const Text('اللغة'),
              subtitle: Text(_getLanguageText(_selectedLanguage)),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: _showLanguageDialog,
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.palette, color: AppColors.primaryBlue),
              title: const Text('المظهر'),
              subtitle: Text(_getThemeText(_selectedTheme)),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: _showThemeDialog,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إعدادات الإشعارات',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.primaryBlue,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            SwitchListTile(
              secondary: const Icon(
                Icons.notifications,
                color: AppColors.primaryBlue,
              ),
              title: const Text('تفعيل الإشعارات'),
              subtitle: const Text('استقبال إشعارات النظام'),
              value: _notificationsEnabled,
              onChanged: (value) {
                setState(() {
                  _notificationsEnabled = value;
                });
              },
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.schedule, color: AppColors.primaryBlue),
              title: const Text('تذكيرات الحضور'),
              subtitle: const Text('إعداد تذكيرات تسجيل الحضور'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () {
                // TODO: Navigate to reminder settings
              },
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.email, color: AppColors.primaryBlue),
              title: const Text('إشعارات البريد الإلكتروني'),
              subtitle: const Text('إعداد إشعارات البريد الإلكتروني'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () {
                // TODO: Navigate to email notification settings
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLocationSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إعدادات الموقع',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.primaryBlue,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            SwitchListTile(
              secondary: const Icon(
                Icons.location_on,
                color: AppColors.primaryBlue,
              ),
              title: const Text('تتبع الموقع'),
              subtitle: const Text('تفعيل تتبع موقع الموظفين'),
              value: _locationTrackingEnabled,
              onChanged: (value) {
                setState(() {
                  _locationTrackingEnabled = value;
                });
              },
            ),
            const Divider(),
            ListTile(
              leading: const Icon(
                Icons.gps_fixed,
                color: AppColors.primaryBlue,
              ),
              title: const Text('دقة الموقع'),
              subtitle: const Text('إعداد دقة تحديد الموقع المطلوبة'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () {
                // TODO: Navigate to location accuracy settings
              },
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.map, color: AppColors.primaryBlue),
              title: const Text('إدارة المواقع'),
              subtitle: const Text('إضافة وتعديل مواقع العمل'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () =>
                  Navigator.pushNamed(context, AppRoutes.sitesManagement),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDataSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إعدادات البيانات',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.primaryBlue,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            SwitchListTile(
              secondary: const Icon(Icons.backup, color: AppColors.primaryBlue),
              title: const Text('النسخ الاحتياطي التلقائي'),
              subtitle: const Text('إنشاء نسخة احتياطية تلقائياً'),
              value: _autoBackupEnabled,
              onChanged: (value) {
                setState(() {
                  _autoBackupEnabled = value;
                });
              },
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.download, color: AppColors.primaryBlue),
              title: const Text('تصدير البيانات'),
              subtitle: const Text('تصدير بيانات الحضور والموظفين'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: _showExportDialog,
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.delete_sweep, color: AppColors.error),
              title: const Text('مسح البيانات'),
              subtitle: const Text('مسح جميع البيانات المحفوظة'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: _showClearDataDialog,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSecuritySettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إعدادات الأمان',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.primaryBlue,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            ListTile(
              leading: const Icon(Icons.lock, color: AppColors.primaryBlue),
              title: const Text('تغيير كلمة المرور'),
              subtitle: const Text('تحديث كلمة مرور الحساب'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () =>
                  Navigator.pushNamed(context, AppRoutes.changePassword),
            ),
            const Divider(),
            ListTile(
              leading: const Icon(
                Icons.fingerprint,
                color: AppColors.primaryBlue,
              ),
              title: const Text('المصادقة البيومترية'),
              subtitle: const Text('تفعيل بصمة الإصبع أو الوجه'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () {
                // TODO: Navigate to biometric settings
              },
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.history, color: AppColors.primaryBlue),
              title: const Text('سجل النشاط'),
              subtitle: const Text('عرض سجل نشاط الحساب'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () {
                // TODO: Navigate to activity log
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAboutSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'حول التطبيق',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.primaryBlue,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            ListTile(
              leading: const Icon(Icons.info, color: AppColors.primaryBlue),
              title: const Text('معلومات التطبيق'),
              subtitle: Text('الإصدار ${AppConstants.appVersion}'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () => Navigator.pushNamed(context, AppRoutes.about),
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.help, color: AppColors.primaryBlue),
              title: const Text('المساعدة والدعم'),
              subtitle: const Text('الحصول على المساعدة'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () {
                // TODO: Navigate to help screen
              },
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.logout, color: AppColors.error),
              title: const Text('تسجيل الخروج'),
              subtitle: const Text('تسجيل الخروج من الحساب'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: _showLogoutDialog,
            ),
          ],
        ),
      ),
    );
  }

  String _getLanguageText(String language) {
    switch (language) {
      case 'ar':
        return 'العربية';
      case 'en':
        return 'English';
      default:
        return 'العربية';
    }
  }

  String _getThemeText(String theme) {
    switch (theme) {
      case 'light':
        return 'فاتح';
      case 'dark':
        return 'داكن';
      case 'system':
        return 'تلقائي';
      default:
        return 'فاتح';
    }
  }

  void _showLanguageDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختيار اللغة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('العربية'),
              value: 'ar',
              groupValue: _selectedLanguage,
              onChanged: (value) {
                setState(() {
                  _selectedLanguage = value!;
                });
                Navigator.pop(context);
              },
            ),
            RadioListTile<String>(
              title: const Text('English'),
              value: 'en',
              groupValue: _selectedLanguage,
              onChanged: (value) {
                setState(() {
                  _selectedLanguage = value!;
                });
                Navigator.pop(context);
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  void _showThemeDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختيار المظهر'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('فاتح'),
              value: 'light',
              groupValue: _selectedTheme,
              onChanged: (value) {
                setState(() {
                  _selectedTheme = value!;
                });
                Navigator.pop(context);
              },
            ),
            RadioListTile<String>(
              title: const Text('داكن'),
              value: 'dark',
              groupValue: _selectedTheme,
              onChanged: (value) {
                setState(() {
                  _selectedTheme = value!;
                });
                Navigator.pop(context);
              },
            ),
            RadioListTile<String>(
              title: const Text('تلقائي'),
              value: 'system',
              groupValue: _selectedTheme,
              onChanged: (value) {
                setState(() {
                  _selectedTheme = value!;
                });
                Navigator.pop(context);
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  void _showExportDialog() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.picture_as_pdf),
              title: const Text('تصدير كـ PDF'),
              onTap: () {
                Navigator.pop(context);
                _exportData('pdf');
              },
            ),
            ListTile(
              leading: const Icon(Icons.table_chart),
              title: const Text('تصدير كـ Excel'),
              onTap: () {
                Navigator.pop(context);
                _exportData('excel');
              },
            ),
            ListTile(
              leading: const Icon(Icons.text_snippet),
              title: const Text('تصدير كـ CSV'),
              onTap: () {
                Navigator.pop(context);
                _exportData('csv');
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showClearDataDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد مسح البيانات'),
        content: const Text(
          'هل أنت متأكد من مسح جميع البيانات؟ لا يمكن التراجع عن هذا الإجراء.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _clearData();
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            child: const Text('مسح البيانات'),
          ),
        ],
      ),
    );
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد تسجيل الخروج'),
        content: const Text('هل أنت متأكد من تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              await context.read<AuthProvider>().logout();
              if (mounted) {
                Navigator.pushNamedAndRemoveUntil(
                  context,
                  AppRoutes.login,
                  (route) => false,
                );
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            child: const Text('تسجيل الخروج'),
          ),
        ],
      ),
    );
  }

  void _exportData(String format) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('سيتم تصدير البيانات كـ $format قريباً')),
    );
  }

  void _clearData() {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('تم مسح البيانات بنجاح')));
  }
}
