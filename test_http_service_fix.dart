import 'dart:convert';

/// Test pour valider la correction du HttpService
/// Simule exactement le comportement du HttpService avant et après la correction
void main() {
  print('🔧 Test de la Correction du HttpService');
  print('======================================\n');

  // Simuler la réponse JSON du backend
  final Map<String, dynamic> backendResponse = {
    "success": true,
    "data": [
      {
        "id": 46,
        "user": {
          "id": 5,
          "name": "سماح زهراوي",
          "email": "<EMAIL>"
        },
        "site": {
          "id": 5,
          "name": "ain soltan"
        },
        "debut_pointage": "2025-08-03 00:13:29",
        "fin_pointage": "2025-08-03 00:13:54",
        "duree": "00:00:25"
      },
      {
        "id": 45,
        "user": {
          "id": 5,
          "name": "سماح زهراوي",
          "email": "<EMAIL>"
        },
        "site": {
          "id": 5,
          "name": "ain soltan"
        },
        "debut_pointage": "2025-08-03 00:12:26",
        "fin_pointage": "2025-08-03 00:12:43",
        "duree": "00:00:17"
      }
    ]
  };

  print('📊 Données de test:');
  print('   success: ${backendResponse['success']}');
  print('   data type: ${backendResponse['data'].runtimeType}');
  print('   data length: ${(backendResponse['data'] as List).length}');
  print('');

  // Test 1: Comportement AVANT la correction
  print('❌ Test 1: Comportement AVANT la correction');
  print('--------------------------------------------');
  final resultBefore = handleResponseBefore(backendResponse, null);
  print('   success: ${resultBefore['success']}');
  print('   data: ${resultBefore['data']}');
  print('   data is null: ${resultBefore['data'] == null}');
  print('   ❌ PROBLÈME: data est null malgré les données présentes');
  print('');

  // Test 2: Comportement APRÈS la correction
  print('✅ Test 2: Comportement APRÈS la correction');
  print('---------------------------------------------');
  final resultAfter = handleResponseAfter(backendResponse, null);
  print('   success: ${resultAfter['success']}');
  print('   data type: ${resultAfter['data'].runtimeType}');
  print('   data is null: ${resultAfter['data'] == null}');
  if (resultAfter['data'] is List) {
    print('   data length: ${(resultAfter['data'] as List).length}');
    print('   first item: ${(resultAfter['data'] as List).first}');
  }
  print('   ✅ CORRIGÉ: data contient maintenant les vraies données');
  print('');

  // Test 3: Avec fromJson (comportement normal)
  print('🔄 Test 3: Avec fromJson (comportement normal)');
  print('-----------------------------------------------');
  final resultWithFromJson = handleResponseAfter(backendResponse, (data) => data);
  print('   success: ${resultWithFromJson['success']}');
  print('   data type: ${resultWithFromJson['data'].runtimeType}');
  print('   data is null: ${resultWithFromJson['data'] == null}');
  print('   ✅ Fonctionne aussi avec fromJson');
  print('');

  print('🎉 Résumé:');
  print('==========');
  print('✅ La correction permet de récupérer les données brutes quand fromJson est null');
  print('✅ Le comportement avec fromJson reste inchangé');
  print('✅ Le fallback des rapports devrait maintenant fonctionner');
  print('');
  print('🚀 Prochaines étapes:');
  print('1. Compilez l\'application avec la correction');
  print('2. Testez la génération de rapport pour l\'employé 5');
  print('3. Vérifiez les nouveaux logs du HttpService');
  print('4. Confirmez que response.data n\'est plus null');
}

/// Simulation du comportement AVANT la correction
Map<String, dynamic> handleResponseBefore(Map<String, dynamic> data, Function? fromJson) {
  final success = data['success'] ?? false;
  final message = data['message'] ?? '';
  final responseData = data['data'];
  
  // AVANT: parsedData n'était assigné que si fromJson != null
  dynamic parsedData;
  if (responseData != null && fromJson != null) {
    parsedData = fromJson(responseData);
  }
  // Si fromJson est null, parsedData reste null !
  
  return {
    'success': success,
    'message': message,
    'data': parsedData, // ❌ null même si responseData contient des données
  };
}

/// Simulation du comportement APRÈS la correction
Map<String, dynamic> handleResponseAfter(Map<String, dynamic> data, Function? fromJson) {
  final success = data['success'] ?? false;
  final message = data['message'] ?? '';
  final responseData = data['data'];
  
  // APRÈS: parsedData est assigné même si fromJson est null
  dynamic parsedData;
  if (responseData != null) {
    if (fromJson != null) {
      // Parse avec la fonction fournie
      parsedData = fromJson(responseData);
    } else {
      // Retourner les données brutes si pas de fonction de parsing
      parsedData = responseData;
    }
  }
  
  return {
    'success': success,
    'message': message,
    'data': parsedData, // ✅ Contient maintenant les vraies données
  };
}

/// Test de validation complète
void validateFix() {
  print('\n🧪 Validation Complète de la Correction');
  print('========================================');
  
  // Cas 1: Réponse avec données et sans fromJson (cas du fallback)
  final testCase1 = {
    "success": true,
    "data": [{"id": 1, "name": "test"}]
  };
  
  final result1 = handleResponseAfter(testCase1, null);
  assert(result1['success'] == true);
  assert(result1['data'] != null);
  assert(result1['data'] is List);
  assert((result1['data'] as List).length == 1);
  print('✅ Cas 1: Données sans fromJson - OK');
  
  // Cas 2: Réponse avec données et avec fromJson
  final result2 = handleResponseAfter(testCase1, (data) => data);
  assert(result2['success'] == true);
  assert(result2['data'] != null);
  assert(result2['data'] is List);
  print('✅ Cas 2: Données avec fromJson - OK');
  
  // Cas 3: Réponse sans données
  final testCase3 = {
    "success": false,
    "message": "No data"
  };
  
  final result3 = handleResponseAfter(testCase3, null);
  assert(result3['success'] == false);
  assert(result3['data'] == null);
  print('✅ Cas 3: Pas de données - OK');
  
  print('');
  print('🎯 Tous les tests de validation passent !');
  print('   La correction est prête pour la production.');
}

/// Exécuter la validation
void runValidation() {
  try {
    validateFix();
  } catch (e) {
    print('❌ Erreur de validation: $e');
  }
}
