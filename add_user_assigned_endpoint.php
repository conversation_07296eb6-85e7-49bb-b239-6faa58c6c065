<?php

// Script pour ajouter l'endpoint user-assigned au backend
echo "=== AJOUT DE L'ENDPOINT USER-ASSIGNED ===\n";

$apiFilePath = 'C:/wamp64/www/clockin/routes/api.php';

// Lire le contenu actuel
$content = file_get_contents($apiFilePath);

// Vérifier si l'endpoint existe déjà
if (strpos($content, '/sites/user-assigned') !== false) {
    echo "✅ L'endpoint /sites/user-assigned existe déjà!\n";
    exit;
}

// Code à ajouter avant la fermeture du groupe middleware
$newEndpoint = '
    // Endpoint pour récupérer le site assigné à l\'utilisateur connecté
    Route::get(\'/sites/user-assigned\', function (Request $request) {
        $user = $request->user();
        
        if (!$user) {
            return response()->json([
                \'success\' => false, 
                \'message_ar\' => \'المستخدم غير مصادق عليه\'
            ], 401);
        }
        
        // Récupérer le site via la table assignments
        $assignment = DB::table(\'assignments\')
            ->join(\'sites\', \'assignments.site_id\', \'=\', \'sites.id\')
            ->where(\'assignments.user_id\', $user->id)
            ->select(
                \'sites.id\',
                \'sites.name\',
                \'sites.latitude\',
                \'sites.longitude\',
                \'sites.created_at\',
                \'sites.updated_at\'
            )
            ->first();
        
        if ($assignment) {
            return response()->json([
                \'success\' => true,
                \'data\' => [
                    \'id\' => $assignment->id,
                    \'name\' => $assignment->name,
                    \'latitude\' => $assignment->latitude,
                    \'longitude\' => $assignment->longitude,
                    \'created_at\' => $assignment->created_at,
                    \'updated_at\' => $assignment->updated_at
                ],
                \'message\' => \'Site assigné trouvé\',
                \'message_ar\' => \'تم العثور على الموقع المخصص\'
            ]);
        }
        
        return response()->json([
            \'success\' => false,
            \'message\' => \'Aucun site assigné trouvé\',
            \'message_ar\' => \'لم يتم العثور على موقع مخصص\'
        ], 404);
    });
';

// Trouver la position de la dernière fermeture du groupe middleware
$lastClosingBrace = strrpos($content, '});');

if ($lastClosingBrace !== false) {
    // Insérer le nouvel endpoint avant la dernière fermeture
    $newContent = substr($content, 0, $lastClosingBrace) . $newEndpoint . "\n" . substr($content, $lastClosingBrace);
    
    // Sauvegarder le fichier modifié
    if (file_put_contents($apiFilePath, $newContent)) {
        echo "✅ Endpoint /sites/user-assigned ajouté avec succès!\n";
        echo "\n📋 DÉTAILS DE L'ENDPOINT:\n";
        echo "URL: GET /api/sites/user-assigned\n";
        echo "Authentification: Token requis\n";
        echo "Accessible par: Tous les utilisateurs authentifiés\n";
        echo "\n🧪 TESTER AVEC POSTMAN:\n";
        echo "GET http://192.168.0.50:8000/api/sites/user-assigned\n";
        echo "Headers:\n";
        echo "  Authorization: Bearer {employee_token}\n";
        echo "  Accept: application/json\n";
        echo "\n📱 REDÉMARRER L'APPLICATION FLUTTER POUR TESTER\n";
    } else {
        echo "❌ Erreur lors de la sauvegarde du fichier!\n";
    }
} else {
    echo "❌ Impossible de trouver la structure du fichier routes/api.php!\n";
}

echo "\n" . str_repeat("=", 50) . "\n";
