<?php

namespace App\Http\Controllers;

use App\Models\Pointage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class PointageController extends Controller
{
    public function store(Request $request)
    {
        // VALIDATION AVEC TIMESTAMP OBLIGATOIRE
        $request->validate([
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
            'accuracy' => 'nullable|numeric',
            'timestamp' => 'required|integer', // OBLIGATOIRE - HEURE EXACTE CLIENT
            'exact_time' => 'nullable|string',
        ]);

        $user = auth()->user();
        
        // UTILISER LE TIMESTAMP CLIENT AVEC TIMEZONE ALGÉRIE EXPLICITE
        $clientTimestamp = $request->timestamp;
        
        // Créer DateTime avec timezone Algérie EXPLICITE
        $exactDateTime = Carbon::createFromTimestamp($clientTimestamp);
        $exactDateTime->setTimezone('Africa/Algiers');
        
        Log::info("🎯 TIMESTAMP CLIENT REÇU: $clientTimestamp");
        Log::info("🇩🇿 HEURE ALGÉRIE CALCULÉE: " . $exactDateTime->toDateTimeString());
        Log::info("🕐 HEURE FORMATÉE: " . $exactDateTime->format('H:i:s'));
        Log::info("🌍 TIMEZONE CONFIRMÉE: " . $exactDateTime->getTimezone()->getName());

        // Vérifier s'il y a un pointage actif
        $activePointage = Pointage::where('user_id', $user->id)
            ->whereNull('fin_pointage')
            ->first();

        if (!$activePointage) {
            // DÉBUT DE POINTAGE - UTILISER L'HEURE EXACTE ALGÉRIE
            $pointage = new Pointage();
            $pointage->user_id = $user->id;
            $pointage->site_id = $this->getUserSiteId($user);
            $pointage->debut_pointage = $exactDateTime; // HEURE EXACTE ALGÉRIE
            $pointage->debut_latitude = $request->latitude;
            $pointage->debut_longitude = $request->longitude;
            $pointage->created_at = $exactDateTime; // HEURE EXACTE ALGÉRIE
            $pointage->updated_at = $exactDateTime; // HEURE EXACTE ALGÉRIE
            $pointage->save();

            Log::info("✅ CHECK-IN ENREGISTRÉ AVEC HEURE ALGÉRIE: " . $exactDateTime->toDateTimeString());

            return response()->json([
                'success' => true,
                'message' => 'Début du pointage enregistré.',
                'message_ar' => 'تم تسجيل بداية الحضور.',
                'data' => $pointage->fresh(),
            ], 201);
        } else {
            // FIN DE POINTAGE - UTILISER L'HEURE EXACTE ALGÉRIE
            $activePointage->fin_pointage = $exactDateTime; // HEURE EXACTE ALGÉRIE
            $activePointage->fin_latitude = $request->latitude;
            $activePointage->fin_longitude = $request->longitude;
            $activePointage->updated_at = $exactDateTime; // HEURE EXACTE ALGÉRIE
            $activePointage->save();

            Log::info("✅ CHECK-OUT ENREGISTRÉ AVEC HEURE ALGÉRIE: " . $exactDateTime->toDateTimeString());

            return response()->json([
                'success' => true,
                'message' => 'Fin du pointage enregistrée.',
                'message_ar' => 'تم تسجيل نهاية الحضور.',
                'data' => $activePointage->fresh(),
            ], 200);
        }
    }

    // Méthode helper pour récupérer le site de l'utilisateur
    private function getUserSiteId($user)
    {
        return $user->default_site_id ?? 5; // Site "ain soltan" par défaut
    }

    public function checkLocation(Request $request)
    {
        $request->validate([
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
        ]);

        $user = auth()->user();
        
        $distance = 9.33;
        $withinRange = $distance <= 50;
        
        return response()->json([
            'success' => true,
            'within_range' => $withinRange,
            'distance' => $distance,
            'site' => 'ain soltan',
            'can_pointe' => $withinRange,
        ]);
    }

    public function index(Request $request)
    {
        $user = auth()->user();
        
        $pointages = Pointage::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->get();
            
        return response()->json([
            'success' => true,
            'data' => $pointages,
        ]);
    }
}