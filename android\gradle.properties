# Optimized Gradle configuration for ClockIn app
org.gradle.jvmargs=-Xmx4G -XX:MaxMetaspaceSize=2G -XX:ReservedCodeCacheSize=256m -XX:+UseG1GC -XX:+UseStringDeduplication
org.gradle.daemon=true
org.gradle.parallel=true
org.gradle.caching=true
org.gradle.configureondemand=true

# Android configuration
android.useAndroidX=true
android.enableJetifier=true
android.enableR8.fullMode=false

# Kotlin configuration
kotlin.code.style=official
